{"version": 3, "sources": ["assets/scripts/game/ChessBoardController1.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIhF,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAY1C;IAAkD,wCAAY;IAA9D;QAAA,qEAg+BC;QA79BG,sBAAgB,GAAc,IAAI,CAAC,CAAE,sBAAsB;QAG3D,eAAS,GAAY,IAAI,CAAC,CAAE,OAAO;QAEnC,OAAO;QACU,gBAAU,GAAG,CAAC,CAAC,CAAE,QAAQ;QACzB,iBAAW,GAAG,GAAG,CAAC,CAAE,QAAQ;QAC5B,kBAAY,GAAG,GAAG,CAAC,CAAE,QAAQ;QAC7B,eAAS,GAAG,EAAE,CAAC,CAAE,gBAAgB;QAElD,SAAS;QACD,cAAQ,GAAiB,EAAE,CAAC,CAAE,aAAa;QAC3C,eAAS,GAAgB,EAAE,CAAC,CAAE,aAAa;QAygBnD,YAAY;QACJ,uBAAiB,GAAgD,EAAE,CAAC;QA0P5E,mBAAmB;QACX,mBAAa,GAAW,CAAC,CAAC;QAC1B,mBAAa,GAAW,CAAC,EAAE,CAAC;;QAyMpC,iBAAiB;IACrB,CAAC;IA98BG,qCAAM,GAAN;QACI,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAED,oCAAK,GAAL;QAAA,iBAOC;QAJG,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,QAAQ;IACA,wCAAS,GAAjB;QACI,UAAU;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;oBAClB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzC,SAAS,EAAE,KAAK;iBACnB,CAAC;aACL;SACJ;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc;IACN,8CAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1B,OAAO;SACV;QAED,oBAAoB;QACpB,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACN,0DAA2B,GAAnC;QAGI,eAAe;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAGvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAExB,cAAc;YACd,IAAI,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;aAC9C;iBAAM;gBACH,oBAAoB;gBACpB,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC9B,IAAI,QAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;gBACrD,IAAI,QAAM,EAAE;oBACR,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAM,CAAC,CAAC,EAAE,QAAM,CAAC,CAAC,CAAC,CAAC;oBACrD,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC1D,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,CAAC,QAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;iBAC9C;aACJ;SACJ;IAEL,CAAC;IAED,cAAc;IACN,0DAA2B,GAAnC,UAAoC,QAAgB;QAChD,mBAAmB;QACnB,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC/C,IAAI,KAAK,EAAE;YACP,OAAO,EAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY;IACJ,4DAA6B,GAArC,UAAsC,GAAY;QAC9C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAc;IACN,mDAAoB,GAA5B,UAA6B,QAAiB,EAAE,CAAS,EAAE,CAAS;QAApE,iBAuBC;QArBG,eAAe;QACf,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,KAA0B;YAEhE,KAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,oBAAoB;QACpB,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAC/C,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;YAExB,WAAW;YACX,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;gBAEpB,KAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;YACjC,CAAC,EAAE,IAAI,CAAC,CAAC;SACZ;IAGL,CAAC;IAED,yBAAyB;IACjB,mDAAoB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,YAAY;QACZ,0BAA0B;QAC1B,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QAChF,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAEjF,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,SAAS;IACD,0CAAW,GAAnB,UAAoB,CAAS,EAAE,CAAS,EAAE,MAA4B;QAGlE,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAE/B,OAAO;SACV;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAGnC,IAAI,QAAQ,CAAC,SAAS,EAAE;YAEpB,OAAO;SACV;QAID,cAAc;QACd,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7B,eAAe;QACf,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAGlC,CAAC;IAED,cAAc;IACN,gDAAiB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QAG1C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACzD,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACjD,OAAO;SACV;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAGnC,WAAW;QAEX,IAAI,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAGvD,qBAAqB;QACrB,IAAI,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAG1D,OAAO;QACP,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAExC,oBAAoB;QACpB,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;QAG1B,eAAe;QACf,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,kBAAkB;QAClB,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE;YAC1C,YAAY;YACZ,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;QAE7B,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;IAGrC,CAAC;IAED,0BAA0B;IAClB,uDAAwB,GAAhC,UAAiC,CAAS,EAAE,CAAS;QACjD,WAAW;QACX,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QAEjC,+BAA+B;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,EAAE;YAChB,IAAI,OAAO,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;YAE/D,OAAO,QAAQ,CAAC;SACnB;QAED,mBAAmB;QAGnB,WAAW;QACX,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACnF,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAEpF,OAAO;QACP,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAC/B,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAE/B,IAAI,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAG1C,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,wBAAwB;IAChB,kDAAmB,GAA3B,UAA4B,UAAmB;QAG3C,oBAAoB;QACpB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YAGR,kBAAkB;YAClB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YAGvB,OAAO;YACP,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAGvC;aAAM;YAEH,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACvC;QAED,oBAAoB;QACpB,oCAAoC;IACxC,CAAC;IAED,wBAAwB;IAChB,8CAAe,GAAvB,UAAwB,UAAmB;QAGvC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACvB,UAAU;YACV,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;YAC9E,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAEpE,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACjC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAG9C;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,+DAAa,CAAC,CAAC;YAC7B,UAAU;YACV,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACvC;IACL,CAAC;IAED,gBAAgB;IACR,qDAAsB,GAA9B,UAA+B,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,UAAsB;QAG5F,2BAA2B;QAC3B,IAAI,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC;YAChD,UAAU,CAAC,YAAY,CAAC,uBAAuB,CAAC;YAChD,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;QAEhF,IAAI,gBAAgB,EAAE;YAGlB,iBAAiB;YACjB,IAAI,gBAAgB,CAAC,MAAM,EAAE;gBAGzB,wBAAwB;gBACxB,IAAI,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBACnE,IAAI,CAAC,YAAY,EAAE;oBACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBAC9C,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;iBAClE;gBAED,eAAe;gBACf,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;gBACtC,gBAAgB,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;aAGzC;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACvD,UAAU,EAAE,CAAC,CAAC,aAAa;gBAC3B,OAAO;aACV;YAED,SAAS;YACT,IAAI,QAAQ,GAAG;gBACX,MAAM,EAAE,YAAU,CAAC,SAAI,CAAG;gBAC1B,QAAQ,EAAE,kBAAM,CAAC,SAAI,CAAC,MAAG;gBACzB,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAClC,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;aACE,CAAC;YAId,eAAe;YACf,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SAE9E;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAEpD,sBAAsB;YACtB,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;SAChE;IACL,CAAC;IAED,sBAAsB;IACd,gDAAiB,GAAzB,UAA0B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAAnE,iBAgEC;QA7DG,2BAA2B;QAC3B,IAAI,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC;YAChD,UAAU,CAAC,YAAY,CAAC,uBAAuB,CAAC;YAChD,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;QAEhF,IAAI,gBAAgB,EAAE;YAGlB,iBAAiB;YACjB,IAAI,gBAAgB,CAAC,MAAM,EAAE;gBAGzB,wBAAwB;gBACxB,IAAI,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBACnE,IAAI,CAAC,YAAY,EAAE;oBACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBAC9C,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;iBAClE;gBAED,eAAe;gBACf,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;gBACtC,gBAAgB,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;aAGzC;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACvD,OAAO;aACV;YAED,SAAS;YACT,IAAI,QAAQ,GAAG;gBACX,MAAM,EAAE,YAAU,CAAC,SAAI,CAAG;gBAC1B,QAAQ,EAAE,kBAAM,CAAC,SAAI,CAAC,MAAG;gBACzB,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAClC,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;aACE,CAAC;YAId,eAAe;YACf,IAAI;gBACA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEnC,eAAe;gBACf,IAAI,CAAC,YAAY,CAAC;oBACd,KAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1D,CAAC,EAAE,GAAG,CAAC,CAAC;aAEX;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;aACtC;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAEpD,sBAAsB;YACtB,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC/C;IACL,CAAC;IAED,aAAa;IACL,gDAAiB,GAAzB,UAA0B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAC/D,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,KAAK,CAAC,yBAAQ,CAAC,SAAI,CAAC,wCAAiB,CAAC,CAAC;YAC/C,OAAO;SACV;QAED,IAAI,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,yBAAQ,CAAC,SAAI,CAAC,4DAAsB,CAAC,CAAC;YACpD,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC,+BAAS,CAAC,SAAI,CAAC,uFAA6B,CAAC,CAAC;YAE3D,oBAAoB;YACpB,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5C;aAAM;SAEN;IAGL,CAAC;IAED,eAAe;IACP,gDAAiB,GAAzB,UAA0B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAE/D,IAAI,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SAC/C;QAED,cAAc;QACd,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,MAAM,GAAG;YACT,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACpB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACnB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACnB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAE,KAAK;SAC9B,CAAC;QAEF,IAAI,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACzC,IAAI,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QAE/B,OAAO,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjD,OAAO;QACP,UAAU,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;IAG7B,CAAC;IAED,sCAAsC;IAC9B,mDAAoB,GAA5B,UAA6B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAElE,mBAAmB;QACnB,IAAI,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,UAAU,EAAE;YAEZ,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5C;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEhC,YAAY;SAEf;IACL,CAAC;IAED,YAAY;IACJ,kDAAmB,GAA3B;QACI,aAAa;QACb,OAAO,uEAAuE,CAAC;IACnF,CAAC;IAED,oBAAoB;IACZ,iDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS;QAC3C,qBAAqB;QAGrB,mBAAmB;QACnB,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC,gBAAgB;QAChB,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,WAAW;IACH,qDAAsB,GAA9B,UAA+B,CAAS,EAAE,CAAS;QAC/C,SAAS;QACT,IAAI,QAAQ,GAAG;YACX,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE;SACtC,CAAC;QAGF,mBAAmB;QACnB,OAAO,QAAQ,CAAC;IACpB,CAAC;IAKO,qDAAsB,GAA9B,UAA+B,CAAS,EAAE,CAAS;QAC/C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACxB,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC;IAGP,CAAC;IAED,eAAe;IACP,iDAAkB,GAA1B;QACI,oBAAoB;QACpB,OAAO,YAAY,CAAC,CAAE,OAAO;IACjC,CAAC;IAED,cAAc;IACP,0CAAW,GAAlB,UAAmB,CAAS,EAAE,CAAS;QACnC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAChE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,YAAY;IACL,8CAAe,GAAtB,UAAuB,CAAS,EAAE,CAAS;QACvC,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAClC,OAAO,KAAK,CAAC;SAChB;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,UAAU,EAAE;YACrB,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACvC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;SAC9B;QAED,OAAO;QACP,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;QAG3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,SAAS;IACF,8CAAe,GAAtB;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC9B;SACJ;IAEL,CAAC;IAED,eAAe;IACR,sDAAuB,GAA9B;QACI,IAAI,WAAW,GAA6B,EAAE,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;oBAC/B,WAAW,CAAC,IAAI,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC;iBAClC;aACJ;SACJ;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,WAAW;IACJ,gDAAiB,GAAxB,UAAyB,CAAS,EAAE,CAAS;QACzC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;IAC1E,CAAC;IAED,WAAW;IACJ,0CAAW,GAAlB,UAAmB,CAAS,EAAE,CAAS;QACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1C,CAAC;IAED,WAAW;IACJ,mDAAoB,GAA3B;QACI,sBAAW,IAAI,CAAC,iBAAiB,EAAE,CAAE,OAAO;IAChD,CAAC;IAED,WAAW;IACJ,qDAAsB,GAA7B;QACI,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;IAEhC,CAAC;IAED,eAAe;IACR,4DAA6B,GAApC,UAAqC,QAAiB;QAClD,mBAAmB;QACnB,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAE7D,SAAS;QACT,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1E,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,eAAe;IACR,4CAAa,GAApB,UAAqB,CAAS,EAAE,CAAS,EAAE,SAAyB;QAAzB,0BAAA,EAAA,gBAAyB;QAChE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,QAAQ,EAAE;YACV,yBAAyB;YACzB,IAAI,SAAS,EAAE;gBACX,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;aAEpC;iBAAM;gBACH,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;aAEnC;SACJ;IACL,CAAC;IAED,qBAAqB;IACd,gDAAiB,GAAxB,UAAyB,WAAqC;QAA9D,iBAQC;QALG,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;YACrB,IAAI,KAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;gBAChF,KAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;aAC5C;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB;IACT,gDAAiB,GAAxB;QAEI,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACP,wCAAS,GAAhB,UAAiB,CAAS,EAAE,CAAS;QAEjC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,gBAAgB;IACT,2CAAY,GAAnB;QACI,IAAI,IAAI,GAAG;YACP,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,UAAU,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;YAC7C,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACtE,WAAW,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,MAAM;YAClD,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB;YAC5C,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS;SACjC,CAAC;QAGF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,uBAAuB;IAChB,yCAAU,GAAjB,UAAkB,CAAS,EAAE,CAAS;QAGlC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,cAAc;QACd,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,UAAQ,CAAC,SAAI,CAAG,CAAC,CAAC;QAE7C,WAAW;QACX,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QACjF,OAAO,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjD,OAAO;QACP,QAAQ,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEhC,OAAO;QACP,IAAI,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAE1B,SAAS;QACT,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC7C,KAAK,CAAC,MAAM,GAAG,MAAI,CAAC,SAAI,CAAC,MAAG,CAAC;QAC7B,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAClC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE7B,oBAAoB;QACpB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAGvC,CAAC;IAED,WAAW;IACJ,6CAAc,GAArB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC/C,QAAQ,CAAC,OAAO,CAAC,UAAA,KAAK;gBAClB,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;oBAChC,KAAK,CAAC,gBAAgB,EAAE,CAAC;iBAC5B;YACL,CAAC,CAAC,CAAC;SACN;IAEL,CAAC;IAED,6BAA6B;IACtB,gDAAiB,GAAxB;QAEI,WAAW;QACX,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC;IACpD,CAAC;IAED,mBAAmB;IACZ,6CAAc,GAArB;QACI,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;SAEzB;IACL,CAAC;IAED,aAAa;IACN,4CAAa,GAApB;QACI,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SAE1B;IACL,CAAC;IAMD,WAAW;IACJ,8CAAe,GAAtB,UAAuB,OAAe,EAAE,OAAe;QACnD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;IAEjC,CAAC;IAED,UAAU;IACH,+CAAgB,GAAvB;QACI,OAAO,EAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,IAAI,CAAC,aAAa,EAAC,CAAC;IAC1D,CAAC;IAED,UAAU;IACH,6CAAc,GAArB,UAAsB,CAAS,EAAE,CAAS,EAAE,OAAe,EAAE,OAAe;QAGxE,WAAW;QACX,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QAEzC,SAAS;QACT,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,OAAO;QACP,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtB,QAAQ;QACR,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IAC3D,CAAC;IAED,WAAW;IACJ,gDAAiB,GAAxB,UAAyB,CAAS,EAAE,CAAS;QAA7C,iBAwCC;QArCG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACxB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,aAAa;QACb,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7B,SAAS;QACT,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,QAAQ,GAAG,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAGrB,yBAAyB;gBACzB,IAAI,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;gBAC1E,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;oBAGjC,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;oBACvD,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE;qBAEjC;yBAAM;wBACH,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;qBACvC;iBACJ;qBAAM;oBACH,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;iBACvD;aACJ;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;aAC/B;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,UAAU;IACH,mDAAoB,GAA3B;QAGI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACzC,OAAO;SACV;QAED,iBAAiB;QACjB,IAAI,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAIrD,OAAO;QACP,IAAI,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAC/D,IAAI,UAAU,EAAE;YAEZ,IAAI,UAAU,CAAC,MAAM,EAAE;gBAGnB,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;aAE1D;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;aAClC;SACJ;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QAED,UAAU;QAEV,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAEnC,SAAS;QACT,QAAQ,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAED,WAAW;IACH,+CAAgB,GAAxB,UAAyB,IAAa,EAAE,KAAa;QACjD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAGhC,KAAkB,UAAa,EAAb,KAAA,IAAI,CAAC,QAAQ,EAAb,cAAa,EAAb,IAAa,EAAE;YAA5B,IAAI,KAAK,SAAA;YACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;SAC3C;IACL,CAAC;IAED,SAAS;IACD,8CAAe,GAAvB,UAAwB,UAAmB,EAAE,GAAW,EAAE,UAAsB;QAAhF,iBAuDC;QArDG,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACjC,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE,EAAE;YACpB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,eAAe;QACf,IAAI,GAAG,GAAG,MAAM,CAAC;QACjB,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC3E,GAAG,GAAG,MAAM,CAAC;SAChB;aAAM,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,GAAG,MAAM,CAAC;SAChB;QAID,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,UAAC,GAAG,EAAE,OAAqB;YACrE,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,KAAK,CAAC,mDAAa,GAAG,CAAC,OAAO,IAAI,GAAG,CAAE,CAAC,CAAC;gBACjD,OAAO,CAAC,KAAK,CAAC,mCAAa,GAAK,CAAC,CAAC;gBAElC,SAAS;gBACT,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzC,UAAU,EAAE,CAAC;gBACb,OAAO;aACV;YAID,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAClC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,YAAY,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEvD,SAAS;YACT,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;YACzB,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;YAGzB,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,sCAAsC;IAC9B,wDAAyB,GAAjC,UAAkC,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,UAAsB;QAG/F,mBAAmB;QACnB,IAAI,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,UAAU,EAAE;YAEZ,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,UAAU,EAAE,CAAC;SAChB;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEhC,YAAY;YAGZ,UAAU,EAAE,CAAC;SAChB;IACL,CAAC;IA19BD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACe;IAGnC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACQ;IANT,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CAg+BxC;IAAD,2BAAC;CAh+BD,AAg+BC,CAh+BiD,EAAE,CAAC,SAAS,GAg+B7D;kBAh+BoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser } from \"../bean/GameBean\";\n\nconst {ccclass, property} = cc._decorator;\n\n// 棋盘格子数据接口\nexport interface GridData {\n    x: number;  // 格子的x坐标 (0-7)\n    y: number;  // 格子的y坐标 (0-7)\n    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置\n    hasPlayer: boolean;  // 是否已经放置了玩家预制体\n    playerNode?: cc.Node;  // 放置的玩家节点引用\n}\n\n@ccclass\nexport default class ChessBoardController extends cc.Component {\n\n    @property(cc.Prefab)\n    playerGamePrefab: cc.Prefab = null;  // player_game_pfb 预制体\n\n    @property(cc.Node)\n    boardNode: cc.Node = null;  // 棋盘节点\n\n    // 棋盘配置\n    private readonly BOARD_SIZE = 8;  // 8x8棋盘\n    private readonly BOARD_WIDTH = 750;  // 棋盘总宽度\n    private readonly BOARD_HEIGHT = 750;  // 棋盘总高度\n    private readonly GRID_SIZE = 88;  // 每个格子的大小 88x88\n\n    // 格子数据存储\n    private gridData: GridData[][] = [];  // 二维数组存储格子数据\n    private gridNodes: cc.Node[][] = [];  // 二维数组存储格子节点\n\n    onLoad() {\n        this.initBoard();\n    }\n\n    start() {\n       \n\n        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成\n        this.scheduleOnce(() => {\n            this.enableTouchForExistingGrids();\n        }, 0.1);\n    }\n\n    // 初始化棋盘\n    private initBoard() {\n        // 初始化数据数组\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            this.gridData[x] = [];\n            this.gridNodes[x] = [];\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                this.gridData[x][y] = {\n                    x: x,\n                    y: y,\n                    worldPos: this.getGridWorldPosition(x, y),\n                    hasPlayer: false\n                };\n            }\n        }\n\n        this.createGridNodes();\n    }\n\n    // 启用现有格子的触摸事件\n    private createGridNodes() {\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置！\");\n            return;\n        }\n\n        // 如果格子已经存在，直接启用触摸事件\n        this.enableTouchForExistingGrids();\n    }\n\n    // 为现有格子启用触摸事件\n    private enableTouchForExistingGrids() {\n        \n\n        // 遍历棋盘节点的所有子节点\n        let children = this.boardNode.children;\n\n\n        for (let i = 0; i < children.length; i++) {\n            let child = children[i];\n\n            // 尝试从节点名称解析坐标\n            let coords = this.parseGridCoordinateFromName(child.name);\n            if (coords) {\n                this.setupGridTouchEvents(child, coords.x, coords.y);\n                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];\n                this.gridNodes[coords.x][coords.y] = child;\n            } else {\n                // 如果无法从名称解析，尝试从位置计算\n                let pos = child.getPosition();\n                let coords = this.getGridCoordinateFromPosition(pos);\n                if (coords) {\n                    this.setupGridTouchEvents(child, coords.x, coords.y);\n                    this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];\n                    this.gridNodes[coords.x][coords.y] = child;\n                }\n            }\n        }\n\n    }\n\n    // 从节点名称解析格子坐标\n    private parseGridCoordinateFromName(nodeName: string): {x: number, y: number} | null {\n        // 尝试匹配 Grid_x_y 格式\n        let match = nodeName.match(/Grid_(\\d+)_(\\d+)/);\n        if (match) {\n            return {x: parseInt(match[1]), y: parseInt(match[2])};\n        }\n        return null;\n    }\n\n    // 从位置计算格子坐标\n    private getGridCoordinateFromPosition(pos: cc.Vec2): {x: number, y: number} | null {\n        let x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);\n        let y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);\n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n        return null;\n    }\n\n    // 为格子节点设置触摸事件\n    private setupGridTouchEvents(gridNode: cc.Node, x: number, y: number) {\n       \n        // 确保节点可以接收触摸事件\n        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {\n           \n            this.onGridClick(x, y, event);\n        }, this);\n\n        // 添加Button组件以确保触摸响应\n        let button = gridNode.getComponent(cc.Button);\n        if (!button) {\n            button = gridNode.addComponent(cc.Button);\n            button.transition = cc.Button.Transition.SCALE;\n            button.zoomScale = 0.95;\n\n            // 添加按钮点击事件\n            button.node.on('click', () => {\n               \n                this.onGridClick(x, y, null);\n            }, this);\n        }\n\n  \n    }\n\n    // 计算格子的世界坐标位置（左下角为(0,0)）\n    private getGridWorldPosition(x: number, y: number): cc.Vec2 {\n        // 计算格子中心点位置\n        // 左下角为(0,0)，所以y坐标需要从下往上计算\n        let posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);\n        let posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);\n        \n        return cc.v2(posX, posY);\n    }\n\n    // 格子点击事件\n    private onGridClick(x: number, y: number, _event?: cc.Event.EventTouch) {\n      \n\n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n          \n            return;\n        }\n\n        let gridData = this.gridData[x][y];\n       \n\n        if (gridData.hasPlayer) {\n            \n            return;\n        }\n\n       \n\n        // 在格子上放置玩家预制体\n        this.placePlayerOnGrid(x, y);\n\n        // 保存坐标，准备发送给后端\n        this.saveGridCoordinate(x, y);\n\n       \n    }\n\n    // 在格子上放置玩家预制体\n    private placePlayerOnGrid(x: number, y: number) {\n        \n\n        if (!this.playerGamePrefab) {\n            console.error(\"❌ 玩家预制体未设置！请在编辑器中设置 playerGamePrefab 属性\");\n            return;\n        }\n\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点未设置！请在编辑器中设置 boardNode 属性\");\n            return;\n        }\n\n        let gridData = this.gridData[x][y];\n        \n\n        // 实例化玩家预制体\n        \n        let playerNode = cc.instantiate(this.playerGamePrefab);\n       \n\n        // 计算正确的位置（考虑现有格子的位置）\n        let correctPosition = this.calculateCorrectPosition(x, y);\n        \n\n        // 设置位置\n        playerNode.setPosition(correctPosition);\n\n        // 先隐藏节点，等头像加载完成后再显示\n        playerNode.active = false;\n       \n\n        // 处理Layout限制问题\n        this.addPlayerNodeSafely(playerNode);\n\n        // 设置头像和用户数据（异步加载）\n        this.setupPlayerAvatarAsync(playerNode, x, y, () => {\n            // 头像加载完成的回调\n            playerNode.active = true;\n           \n        });\n\n        // 更新格子数据\n        gridData.hasPlayer = true;\n        gridData.playerNode = playerNode;\n\n   \n    }\n\n    // 计算正确的位置（格子中心偏移(0, -16)）\n    private calculateCorrectPosition(x: number, y: number): cc.Vec2 {\n        // 使用自定义偏移量\n        let offsetX = this.customOffsetX;\n        let offsetY = this.customOffsetY;\n\n        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移\n        let targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (targetGridNode) {\n            let gridPos = targetGridNode.getPosition();\n            let finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);\n        \n            return finalPos;\n        }\n\n        // 方法2: 基于棋盘的实际位置计算\n       \n\n        // 计算格子中心位置\n        let centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);\n        let centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);\n\n        // 添加偏移\n        let finalX = centerX + offsetX;\n        let finalY = centerY + offsetY;\n\n        let calculatedPos = cc.v2(finalX, finalY);\n       \n\n        return calculatedPos;\n    }\n\n    // 安全地添加玩家节点（处理Layout限制）\n    private addPlayerNodeSafely(playerNode: cc.Node) {\n     \n\n        // 检查棋盘节点是否有Layout组件\n        let layout = this.boardNode.getComponent(cc.Layout);\n        if (layout) {\n           \n\n            // 方案1: 临时禁用Layout\n            layout.enabled = false;\n           \n\n            // 添加节点\n            this.boardNode.addChild(playerNode);\n\n\n        } else {\n           \n            this.boardNode.addChild(playerNode);\n        }\n\n        // 方案2备选：添加到Layout外部\n        // this.addToParentNode(playerNode);\n    }\n\n    // 备选方案：添加到父节点（Layout外部）\n    private addToParentNode(playerNode: cc.Node) {\n      \n\n        if (this.boardNode.parent) {\n            // 需要转换坐标系\n            let worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());\n            let localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);\n\n            playerNode.setPosition(localPos);\n            this.boardNode.parent.addChild(playerNode);\n\n            \n        } else {\n            console.error(`❌ 棋盘节点没有父节点`);\n            // 回退到直接添加\n            this.boardNode.addChild(playerNode);\n        }\n    }\n\n    // 异步设置玩家头像（带回调）\n    private setupPlayerAvatarAsync(playerNode: cc.Node, x: number, y: number, onComplete: () => void) {\n       \n\n        // 查找PlayerGameController组件\n        let playerController = playerNode.getComponent(\"PlayerGameController\") ||\n                              playerNode.getComponent(\"PlayerGameController \") ||\n                              playerNode.getComponentInChildren(\"PlayerGameController\");\n\n        if (playerController) {\n           \n\n            // 检查avatar节点是否存在\n            if (playerController.avatar) {\n              \n\n                // 检查avatar节点是否有Sprite组件\n                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);\n                if (!avatarSprite) {\n                    console.warn(\"⚠️ avatar节点缺少Sprite组件，正在添加...\");\n                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);\n                }\n\n                // 确保avatar节点可见\n                playerController.avatar.active = true;\n                playerController.avatar.opacity = 255;\n\n                \n            } else {\n                console.error(\"❌ PlayerGameController中的avatar节点为null\");\n                onComplete(); // 即使失败也要调用回调\n                return;\n            }\n\n            // 创建用户数据\n            let userData = {\n                userId: `player_${x}_${y}`,\n                nickName: `玩家(${x},${y})`,\n                avatar: this.getDefaultAvatarUrl(),\n                score: 0,\n                pos: 0,\n                coin: 0,\n                status: 0,\n                rank: 0\n            } as RoomUser;\n\n          \n\n            // 使用新的异步头像加载方法\n            this.loadAvatarAsync(playerController.avatar, userData.avatar, onComplete);\n\n        } else {\n            console.warn(\"⚠️ 找不到PlayerGameController组件，跳过头像设置\");\n\n            // 尝试直接在节点上查找avatar子节点\n            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);\n        }\n    }\n\n    // 设置玩家头像（保留原方法用于其他地方）\n    private setupPlayerAvatar(playerNode: cc.Node, x: number, y: number) {\n       \n\n        // 查找PlayerGameController组件\n        let playerController = playerNode.getComponent(\"PlayerGameController\") ||\n                              playerNode.getComponent(\"PlayerGameController \") ||\n                              playerNode.getComponentInChildren(\"PlayerGameController\");\n\n        if (playerController) {\n         \n\n            // 检查avatar节点是否存在\n            if (playerController.avatar) {\n                \n\n                // 检查avatar节点是否有Sprite组件\n                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);\n                if (!avatarSprite) {\n                    console.warn(\"⚠️ avatar节点缺少Sprite组件，正在添加...\");\n                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);\n                }\n\n                // 确保avatar节点可见\n                playerController.avatar.active = true;\n                playerController.avatar.opacity = 255;\n\n               \n            } else {\n                console.error(\"❌ PlayerGameController中的avatar节点为null\");\n                return;\n            }\n\n            // 创建用户数据\n            let userData = {\n                userId: `player_${x}_${y}`,\n                nickName: `玩家(${x},${y})`,\n                avatar: this.getDefaultAvatarUrl(),\n                score: 0,\n                pos: 0,\n                coin: 0,\n                status: 0,\n                rank: 0\n            } as RoomUser;\n\n            \n\n            // 安全地调用setData\n            try {\n                playerController.setData(userData);\n               \n                // 延迟检查头像是否加载成功\n                this.scheduleOnce(() => {\n                    this.checkAvatarLoaded(playerController.avatar, x, y);\n                }, 2.0);\n\n            } catch (error) {\n                console.error(\"❌ 设置头像时出错:\", error);\n            }\n        } else {\n            console.warn(\"⚠️ 找不到PlayerGameController组件，跳过头像设置\");\n\n            // 尝试直接在节点上查找avatar子节点\n            this.tryDirectAvatarSetup(playerNode, x, y);\n        }\n    }\n\n    // 检查头像是否加载成功\n    private checkAvatarLoaded(avatarNode: cc.Node, x: number, y: number) {\n        if (!avatarNode) {\n            console.error(`❌ 位置(${x},${y})的avatar节点为null`);\n            return;\n        }\n\n        let sprite = avatarNode.getComponent(cc.Sprite);\n        if (!sprite) {\n            console.error(`❌ 位置(${x},${y})的avatar节点没有Sprite组件`);\n            return;\n        }\n\n        if (!sprite.spriteFrame) {\n            console.warn(`⚠️ 位置(${x},${y})的头像可能加载失败，spriteFrame为null`);\n\n            // 尝试设置一个默认的颜色作为备用显示\n            this.setFallbackAvatar(avatarNode, x, y);\n        } else {\n           \n        }\n\n        \n    }\n\n    // 设置备用头像（纯色方块）\n    private setFallbackAvatar(avatarNode: cc.Node, x: number, y: number) {\n       \n        let sprite = avatarNode.getComponent(cc.Sprite);\n        if (!sprite) {\n            sprite = avatarNode.addComponent(cc.Sprite);\n        }\n\n        // 创建一个简单的纯色纹理\n        let texture = new cc.Texture2D();\n        let colors = [\n            [255, 107, 107, 255], // 红色\n            [78, 205, 196, 255],  // 青色\n            [69, 183, 209, 255],  // 蓝色\n            [150, 206, 180, 255], // 绿色\n            [255, 234, 167, 255]  // 黄色\n        ];\n\n        let colorIndex = (x + y) % colors.length;\n        let color = colors[colorIndex];\n\n        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);\n        sprite.spriteFrame = new cc.SpriteFrame(texture);\n\n        // 设置大小\n        avatarNode.setContentSize(80, 80);\n        avatarNode.active = true;\n\n       \n    }\n\n    // 尝试直接设置头像（当找不到PlayerGameController时）\n    private tryDirectAvatarSetup(playerNode: cc.Node, x: number, y: number) {\n        \n        // 查找名为\"avatar\"的子节点\n        let avatarNode = playerNode.getChildByName(\"avatar\");\n        if (avatarNode) {\n           \n            this.setFallbackAvatar(avatarNode, x, y);\n        } else {\n            console.warn(\"⚠️ 未找到avatar子节点\");\n\n            // 列出所有子节点名称\n        \n        }\n    }\n\n    // 获取默认头像URL\n    private getDefaultAvatarUrl(): string {\n        // 使用真实的头像URL\n        return \"https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg\";\n    }\n\n    // 保存格子坐标（用于后续发送给后端）\n    private saveGridCoordinate(x: number, y: number) {\n        // 这里可以将坐标保存到数组或发送给后端\n       \n\n        // 示例：可以调用网络管理器发送坐标\n        this.sendCoordinateToServer(x, y);\n\n        // 或者保存到本地数组以备后用\n        this.addToCoordinateHistory(x, y);\n    }\n\n    // 发送坐标到服务器\n    private sendCoordinateToServer(x: number, y: number) {\n        // 构造发送数据\n        let moveData = {\n            x: x,\n            y: y,\n            timestamp: Date.now(),\n            playerId: this.getCurrentPlayerId()\n        };\n\n\n        // 暂时只是打印，避免未使用变量警告\n        return moveData;\n    }\n\n    // 添加到坐标历史记录\n    private coordinateHistory: {x: number, y: number, timestamp: number}[] = [];\n\n    private addToCoordinateHistory(x: number, y: number) {\n        this.coordinateHistory.push({\n            x: x,\n            y: y,\n            timestamp: Date.now()\n        });\n\n       \n    }\n\n    // 获取当前玩家ID（示例）\n    private getCurrentPlayerId(): string {\n        // 这里应该从全局状态或用户数据中获取\n        return \"player_001\";  // 示例ID\n    }\n\n    // 获取指定坐标的格子数据\n    public getGridData(x: number, y: number): GridData | null {\n        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {\n            return null;\n        }\n        return this.gridData[x][y];\n    }\n\n    // 清除指定格子的玩家\n    public clearGridPlayer(x: number, y: number): boolean {\n        let gridData = this.getGridData(x, y);\n        if (!gridData || !gridData.hasPlayer) {\n            return false;\n        }\n\n        // 移除玩家节点\n        if (gridData.playerNode) {\n            gridData.playerNode.removeFromParent();\n            gridData.playerNode = null;\n        }\n\n        // 更新数据\n        gridData.hasPlayer = false;\n        \n       \n        return true;\n    }\n\n    // 清除所有玩家\n    public clearAllPlayers() {\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                this.clearGridPlayer(x, y);\n            }\n        }\n       \n    }\n\n    // 获取所有已放置玩家的坐标\n    public getAllPlayerCoordinates(): {x: number, y: number}[] {\n        let coordinates: {x: number, y: number}[] = [];\n\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                if (this.gridData[x][y].hasPlayer) {\n                    coordinates.push({x: x, y: y});\n                }\n            }\n        }\n\n        return coordinates;\n    }\n\n    // 检查坐标是否有效\n    public isValidCoordinate(x: number, y: number): boolean {\n        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;\n    }\n\n    // 检查格子是否为空\n    public isGridEmpty(x: number, y: number): boolean {\n        if (!this.isValidCoordinate(x, y)) {\n            return false;\n        }\n        return !this.gridData[x][y].hasPlayer;\n    }\n\n    // 获取坐标历史记录\n    public getCoordinateHistory(): {x: number, y: number, timestamp: number}[] {\n        return [...this.coordinateHistory];  // 返回副本\n    }\n\n    // 清除坐标历史记录\n    public clearCoordinateHistory() {\n        this.coordinateHistory = [];\n      \n    }\n\n    // 根据世界坐标获取格子坐标\n    public getGridCoordinateFromWorldPos(worldPos: cc.Vec2): {x: number, y: number} | null {\n        // 将世界坐标转换为相对于棋盘的坐标\n        let localPos = this.boardNode.convertToNodeSpaceAR(worldPos);\n\n        // 计算格子坐标\n        let x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);\n        let y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);\n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n\n        return null;\n    }\n\n    // 高亮显示格子（可选功能）\n    public highlightGrid(x: number, y: number, highlight: boolean = true) {\n        if (!this.isValidCoordinate(x, y)) {\n            return;\n        }\n\n        let gridNode = this.gridNodes[x][y];\n        if (gridNode) {\n            // 这里可以添加高亮效果，比如改变颜色或添加边框\n            if (highlight) {\n                gridNode.color = cc.Color.YELLOW;\n                \n            } else {\n                gridNode.color = cc.Color.WHITE;\n              \n            }\n        }\n    }\n\n    // 批量放置玩家（用于从服务器同步数据）\n    public batchPlacePlayers(coordinates: {x: number, y: number}[]) {\n       \n\n        coordinates.forEach(coord => {\n            if (this.isValidCoordinate(coord.x, coord.y) && this.isGridEmpty(coord.x, coord.y)) {\n                this.placePlayerOnGrid(coord.x, coord.y);\n            }\n        });\n    }\n\n    // 手动启用触摸事件（调试用）\n    public manualEnableTouch() {\n        \n        this.enableTouchForExistingGrids();\n    }\n\n    // 测试点击功能（调试用）\n    public testClick(x: number, y: number) {\n        \n        this.onGridClick(x, y);\n    }\n\n    // 获取棋盘状态信息（调试用）\n    public getBoardInfo() {\n        let info = {\n            boardSize: this.BOARD_SIZE,\n            gridSize: this.GRID_SIZE,\n            boardWidth: this.BOARD_WIDTH,\n            boardHeight: this.BOARD_HEIGHT,\n            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,\n            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,\n            playerCount: this.getAllPlayerCoordinates().length,\n            hasPlayerGamePrefab: !!this.playerGamePrefab,\n            hasBoardNode: !!this.boardNode\n        };\n\n \n        return info;\n    }\n\n    // 简单测试方法 - 只测试位置，不加载头像\n    public simpleTest(x: number, y: number) {\n       \n\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点未设置\");\n            return;\n        }\n\n        // 创建一个简单的彩色方块\n        let testNode = new cc.Node(`Test_${x}_${y}`);\n\n        // 添加一个彩色方块\n        let sprite = testNode.addComponent(cc.Sprite);\n        let texture = new cc.Texture2D();\n        let color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];\n        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);\n        sprite.spriteFrame = new cc.SpriteFrame(texture);\n\n        // 设置大小\n        testNode.setContentSize(60, 60);\n\n        // 计算位置\n        let pos = this.calculateCorrectPosition(x, y);\n        testNode.setPosition(pos);\n\n        // 添加坐标标签\n        let labelNode = new cc.Node(\"Label\");\n        let label = labelNode.addComponent(cc.Label);\n        label.string = `(${x},${y})`;\n        label.fontSize = 16;\n        label.node.color = cc.Color.WHITE;\n        labelNode.setPosition(0, 0);\n        testNode.addChild(labelNode);\n\n        // 添加到棋盘（处理Layout问题）\n        this.addPlayerNodeSafely(testNode);\n\n        \n    }\n\n    // 清除所有测试节点\n    public clearTestNodes() {\n        if (this.boardNode) {\n            let children = this.boardNode.children.slice();\n            children.forEach(child => {\n                if (child.name.startsWith(\"Test_\")) {\n                    child.removeFromParent();\n                }\n            });\n        }\n        \n    }\n\n    // 切换到父节点添加模式（如果Layout问题仍然存在）\n    public useParentNodeMode() {\n     \n        // 重新定义添加方法\n        this.addPlayerNodeSafely = this.addToParentNode;\n    }\n\n    // 重新启用Layout（如果需要）\n    public reEnableLayout() {\n        let layout = this.boardNode.getComponent(cc.Layout);\n        if (layout) {\n            layout.enabled = true;\n           \n        }\n    }\n\n    // 永久禁用Layout\n    public disableLayout() {\n        let layout = this.boardNode.getComponent(cc.Layout);\n        if (layout) {\n            layout.enabled = false;\n         \n        }\n    }\n\n    // 自定义偏移量（如果需要调整位置）\n    private customOffsetX: number = 0;\n    private customOffsetY: number = -16;\n\n    // 设置自定义偏移量\n    public setCustomOffset(offsetX: number, offsetY: number) {\n        this.customOffsetX = offsetX;\n        this.customOffsetY = offsetY;\n      \n    }\n\n    // 获取当前偏移量\n    public getCurrentOffset(): {x: number, y: number} {\n        return {x: this.customOffsetX, y: this.customOffsetY};\n    }\n\n    // 测试不同偏移量\n    public testWithOffset(x: number, y: number, offsetX: number, offsetY: number) {\n       \n\n        // 临时保存当前偏移\n        let originalOffsetX = this.customOffsetX;\n        let originalOffsetY = this.customOffsetY;\n\n        // 设置测试偏移\n        this.setCustomOffset(offsetX, offsetY);\n\n        // 执行测试\n        this.simpleTest(x, y);\n\n        // 恢复原偏移\n        this.setCustomOffset(originalOffsetX, originalOffsetY);\n    }\n\n    // 测试头像显示功能\n    public testAvatarDisplay(x: number, y: number) {\n     \n\n        if (!this.isValidCoordinate(x, y)) {\n            console.error(\"❌ 无效坐标\");\n            return;\n        }\n\n        if (this.gridData[x][y].hasPlayer) {\n            console.warn(\"⚠️ 该位置已有玩家\");\n            return;\n        }\n\n        // 直接调用放置玩家方法\n        this.placePlayerOnGrid(x, y);\n\n        // 延迟检查结果\n        this.scheduleOnce(() => {\n            let gridData = this.gridData[x][y];\n            if (gridData.playerNode) {\n               \n\n                // 检查PlayerGameController\n                let controller = gridData.playerNode.getComponent(\"PlayerGameController\");\n                if (controller && controller.avatar) {\n                 \n\n                    let sprite = controller.avatar.getComponent(cc.Sprite);\n                    if (sprite && sprite.spriteFrame) {\n                       \n                    } else {\n                        console.warn(\"⚠️ 头像SpriteFrame不存在\");\n                    }\n                } else {\n                    console.warn(\"⚠️ PlayerGameController或avatar节点不存在\");\n                }\n            } else {\n                console.error(\"❌ 玩家节点创建失败\");\n            }\n        }, 3.0);\n    }\n\n    // 调试预制体结构\n    public debugPrefabStructure() {\n        \n\n        if (!this.playerGamePrefab) {\n            console.error(\"❌ playerGamePrefab为null\");\n            return;\n        }\n\n        // 实例化一个临时节点来检查结构\n        let tempNode = cc.instantiate(this.playerGamePrefab);\n\n       \n\n        // 检查组件\n        let controller = tempNode.getComponent(\"PlayerGameController\");\n        if (controller) {\n           \n            if (controller.avatar) {\n               \n\n                let sprite = controller.avatar.getComponent(cc.Sprite);\n               \n            } else {\n                console.error(\"❌ avatar节点不存在\");\n            }\n        } else {\n            console.error(\"❌ 找不到PlayerGameController组件\");\n        }\n\n        // 列出所有子节点\n      \n        this.logNodeHierarchy(tempNode, 0);\n\n        // 清理临时节点\n        tempNode.destroy();\n    }\n\n    // 递归打印节点层级\n    private logNodeHierarchy(node: cc.Node, depth: number) {\n        let indent = \"  \".repeat(depth);\n       \n\n        for (let child of node.children) {\n            this.logNodeHierarchy(child, depth + 1);\n        }\n    }\n\n    // 异步加载头像\n    private loadAvatarAsync(avatarNode: cc.Node, url: string, onComplete: () => void) {\n       \n        if (!avatarNode) {\n            console.error(\"❌ avatar节点为null\");\n            onComplete();\n            return;\n        }\n\n        let avatarSprite = avatarNode.getComponent(cc.Sprite);\n        if (!avatarSprite) {\n            console.warn(\"⚠️ avatar节点没有Sprite组件，正在添加...\");\n            avatarSprite = avatarNode.addComponent(cc.Sprite);\n        }\n\n        if (!url || url === '') {\n            console.warn(\"⚠️ URL为空，设置备用头像\");\n            this.setFallbackAvatar(avatarNode, 0, 0);\n            onComplete();\n            return;\n        }\n\n        // 根据URL判断文件扩展名\n        let ext = '.png';\n        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {\n            ext = '.jpg';\n        } else if (url.toLowerCase().includes('.png')) {\n            ext = '.png';\n        }\n\n       \n\n        cc.assetManager.loadRemote(url, { ext: ext }, (err, texture: cc.Texture2D) => {\n            if (err) {\n                console.error(`❌ 头像加载失败: ${err.message || err}`);\n                console.error(`❌ 失败的URL: ${url}`);\n\n                // 设置备用头像\n                this.setFallbackAvatar(avatarNode, 0, 0);\n                onComplete();\n                return;\n            }\n\n            \n\n            texture.setPremultiplyAlpha(true);\n            texture.packable = false;\n            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);\n\n            // 确保节点可见\n            avatarNode.active = true;\n            avatarNode.opacity = 255;\n\n          \n            onComplete();\n        });\n    }\n\n    // 异步直接设置头像（当找不到PlayerGameController时）\n    private tryDirectAvatarSetupAsync(playerNode: cc.Node, x: number, y: number, onComplete: () => void) {\n     \n\n        // 查找名为\"avatar\"的子节点\n        let avatarNode = playerNode.getChildByName(\"avatar\");\n        if (avatarNode) {\n           \n            this.setFallbackAvatar(avatarNode, x, y);\n            onComplete();\n        } else {\n            console.warn(\"⚠️ 未找到avatar子节点\");\n\n            // 列出所有子节点名称\n           \n           \n            onComplete();\n        }\n    }\n\n    // update (dt) {}\n}\n"]}