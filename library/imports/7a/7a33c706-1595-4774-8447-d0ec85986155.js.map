{"version": 3, "sources": ["assets/scripts/hall/MatchParentController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,yDAAwD;AAExD,iDAAgD;AAChD,qDAAkD;AAClD,6CAA4C;AAC5C,0DAAwE;AACxE,kEAA6D;AAC7D,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAmD,yCAAY;IAA/D;QAAA,qEAiJC;QA9IG,aAAO,GAAY,IAAI,CAAC;QAExB,kBAAY,GAAY,IAAI,CAAC;QAE7B,mBAAa,GAAY,IAAI,CAAC;QAE9B,eAAS,GAAc,IAAI,CAAC;QAIpB,kBAAY,GAA0B,EAAE,CAAC,CAAC,mBAAmB;QAErE,gBAAU,GAAG,cAAQ,CAAC,CAAA;;QA+HtB,iBAAiB;IAGrB,CAAC;IAhIa,sCAAM,GAAhB;QACI,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE;YAC7B,eAAe;YACf,IAAI,iBAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,IAAI,GAAG,EAAE,EAAC,OAAO;gBAC/C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAA;aACjD;SACJ;aAAM;YACH,cAAc;SACjB;IACL,CAAC;IAES,wCAAQ,GAAlB;QACI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAA;IAC9B,CAAC;IAED,qCAAK,GAAL;QAAA,iBASC;QAPG,YAAY;QACZ,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE;YAC1H,IAAI,KAAI,CAAC,SAAS,EAAE;gBAChB,KAAI,CAAC,SAAS,EAAE,CAAA;aACnB;QACL,CAAC,CAAC,CAAC;IAEP,CAAC;IAED,+CAAe,GAAf;QAEI,IAAI,MAAM,GAAe,EAAE,CAAC;QAE5B,SAAS;QACT,IAAI,YAAY,GAAW,uBAAU,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC;QAC5D,IAAI,QAAQ,GAAa,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;QACrE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAER,IAAI,IAAI,GAAa;oBACjB,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,GAAG,EAAE,CAAC;oBACN,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,IAAI,EAAE,CAAC;iBAEV,CAAA;gBACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAErB;iBAAM;gBACH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACrB;SACJ;QACD,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,6BAAmB,CAAC,CAAA;gBACnE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;gBAC9C,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7C;SACJ;aAAM;YACH,IAAI,SAAS,GAAG,aAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAA,UAAU;YACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,6BAAmB,CAAC,CAAA;gBACnE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;gBAC9C,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7C;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAClC,IAAI,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,6BAAmB,CAAC,CAAA;gBACnE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;gBAC9C,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;aAC/C;SACJ;IAIL,CAAC;IAED,QAAQ;IACR,2CAAW,GAAX;QACI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAA,CAAC,eAAe;QAC3C,IAAI,IAAI,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;QACjE,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAlE,CAAkE,CAAC,CAAC,CAAA,IAAI;QAC/G,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAA,CAAC,WAAW;QAC/E,gBAAgB;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;SACJ;QAED,IAAI,CAAC,UAAU,GAAG;YACd,IAAI,eAAe,GAAoB;gBACnC,OAAO,EAAE,+BAAa,CAAC,kBAAkB;gBACzC,MAAM,EAAE,EAAE;aACb,CAAA;YACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC,CAAA,SAAS;QACxE,CAAC,CAAA;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAA;IAEzC,CAAC;IAGS,yCAAS,GAAnB;QACI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IACpC,CAAC;IAKD,wCAAQ,GAAR,UAAS,SAAmB;QACxB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;IAC9B,CAAC;IAzID;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACY;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACQ;IATX,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CAiJzC;IAAD,4BAAC;CAjJD,AAiJC,CAjJkD,EAAE,CAAC,SAAS,GAiJ9D;kBAjJoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { Publish } from \"../../meshTools/tools/Publish\";\nimport { RoomUser, UserInfo } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { EventType } from \"../common/EventCenter\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { AutoMessageBean, AutoMessageId } from \"../net/MessageBaseBean\";\nimport MatchItemController from \"../pfb/MatchItemController\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class MatchParentController extends cc.Component {\n\n    @property(cc.Node)\n    backBtn: cc.Node = null;\n    @property(cc.Node)\n    matchUserLay: cc.Node = null;\n    @property(cc.Node)\n    matchUserLay2: cc.Node = null;\n    @property(cc.Prefab)\n    matchItem: cc.Prefab = null;\n\n    backClick: Function\n\n    private _userListCol: MatchItemController[] = []; //显示用户布局的controller\n\n    normalTime = () => { }\n\n    protected onLoad(): void {\n        if (cc.sys.os === cc.sys.OS_IOS) {\n            // 在iOS设备上执行的代码\n            if (Publish.GetInstance().gameMode != '2') {//非半屏的话\n                this.backBtn.getComponent(cc.Widget).top = 146\n            }\n        } else {\n            // 在其他设备上执行的代码\n        }\n    }\n\n    protected onEnable(): void {\n        this.backBtn.active = true\n    }\n\n    start() {\n\n        //设置返回键的点击事件\n        Tools.imageButtonClick(this.backBtn, Config.buttonRes + 'board_btn_back_normal', Config.buttonRes + 'board_btn_back_pressed', () => {\n            if (this.backClick) {\n                this.backClick()\n            }\n        });\n\n    }\n\n    createMatchView() {\n\n        let user_s: RoomUser[] = [];\n\n        //判断是几人游戏\n        let peopleNumber: number = GlobalBean.GetInstance().players;\n        let userInfo: UserInfo = GlobalBean.GetInstance().loginData.userInfo;\n        for (let i = 0; i < peopleNumber; i++) {\n            if (i == 0) {\n\n                let user: RoomUser = {\n                    userId: userInfo.userId,\n                    nickName: userInfo.nickname,\n                    avatar: userInfo.avatar,\n                    pos: 0,\n                    coin: 0,\n                    status: 0,\n                    score: 0,\n                    rank: 0\n\n                }\n                user_s.push(user);\n\n            } else {\n                user_s.push(null);\n            }\n        }\n        //这里是添加一个等待的页面匹配的占位\n        this.matchUserLay.removeAllChildren();\n        this.matchUserLay2.removeAllChildren();\n        this._userListCol = [];\n\n        if (user_s.length <= 4) {\n            for (let i = 0; i < user_s.length; i++) {\n                const item = cc.instantiate(this.matchItem);\n                this.matchUserLay.addChild(item);\n                let matchingItemController = item.getComponent(MatchItemController)\n                this._userListCol.push(matchingItemController)\n                matchingItemController.setData(user_s[i]);\n            }\n        } else {\n            let arrayData = Tools.chunkArray(user_s, 3)//根据人数进行分块\n            for (let i = 0; i < arrayData[0].length; i++) {\n                const item = cc.instantiate(this.matchItem);\n                this.matchUserLay.addChild(item);\n                let matchingItemController = item.getComponent(MatchItemController)\n                this._userListCol.push(matchingItemController)\n                matchingItemController.setData(user_s[i]);\n            }\n            for (let i = 0; i < arrayData[1].length; i++) {\n                const item = cc.instantiate(this.matchItem);\n                this.matchUserLay2.addChild(item);\n                let matchingItemController = item.getComponent(MatchItemController)\n                this._userListCol.push(matchingItemController)\n                matchingItemController.setData(user_s[3+i]);\n            }\n        }\n\n\n\n    }\n\n    //设置游戏数据\n    setGameData() {\n        this.backBtn.active = false //匹配成功之后 隐藏掉返回键\n        let user: RoomUser[] = GlobalBean.GetInstance().adjustUserData();\n        const index = user.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索\n        GlobalBean.GetInstance().loginData.userInfo.coin = user[index].coin //更新自己的最新金币\n        //这里是匹配成功之后的真实数据\n        for (let i = 0; i < user.length; i++) {\n            if (i <= this._userListCol.length) {\n                this._userListCol[i].setData(user[i]);\n            }\n        }\n\n        this.normalTime = () => {\n            let autoMessageBean: AutoMessageBean = {\n                'msgId': AutoMessageId.SwitchGameSceneMsg,//匹配成功 通知进入游戏页面\n                'data': {}\n            }\n            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);//进入游戏的消息\n        }\n\n        this.scheduleOnce(this.normalTime, 1)\n\n    }\n\n\n    protected onDisable(): void {\n        this.unschedule(this.normalTime)\n    }\n\n\n\n\n    setClick(backClick: Function) {\n        this.backClick = backClick\n    }\n\n    // update (dt) {}\n\n    \n}\n"]}