"use strict";
cc._RF.push(module, 'eed91BOnAtLKYkInYtuL76o', 'LevelSelectPageController');
// scripts/hall/Level/LevelSelectPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelSelectPageController = /** @class */ (function (_super) {
    __extends(LevelSelectPageController, _super);
    function LevelSelectPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSelectController = null;
        _this.scrollView = null;
        _this.currentLevelLabel = null;
        _this.completeButton = null;
        _this.unlockButton = null;
        _this.resetButton = null;
        // 新增的游戏按钮
        _this.startGameButton = null;
        _this.lockedButton = null;
        return _this;
    }
    LevelSelectPageController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        // 设置按钮事件
        if (this.completeButton) {
            this.completeButton.node.on('click', this.onCompleteButtonClick, this);
        }
        if (this.unlockButton) {
            this.unlockButton.node.on('click', this.onUnlockButtonClick, this);
        }
        if (this.resetButton) {
            this.resetButton.node.on('click', this.onResetButtonClick, this);
        }
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    };
    LevelSelectPageController.prototype.start = function () {
        var _this = this;
        this.updateCurrentLevelDisplay();
        this.updateGameButtons();
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = function (levelNumber) {
                _this.onLevelSelectionChanged(levelNumber);
            };
        }
    };
    /**
     * 更新当前关卡显示
     */
    LevelSelectPageController.prototype.updateCurrentLevelDisplay = function () {
        if (this.currentLevelLabel && this.levelSelectController) {
            var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
            this.currentLevelLabel.string = "\u5F53\u524D\u9009\u4E2D\u5173\u5361: " + currentLevel;
        }
        // 同时更新游戏按钮状态
        this.updateGameButtons();
    };
    /**
     * 完成当前关卡按钮点击
     */
    LevelSelectPageController.prototype.onCompleteButtonClick = function () {
        if (this.levelSelectController) {
            this.levelSelectController.completeCurrentLevel();
            this.updateCurrentLevelDisplay();
            cc.log("完成当前关卡");
        }
    };
    /**
     * 解锁下一关按钮点击
     */
    LevelSelectPageController.prototype.onUnlockButtonClick = function () {
        if (this.levelSelectController) {
            this.levelSelectController.unlockNextLevel();
            this.updateCurrentLevelDisplay();
            cc.log("解锁下一关");
        }
    };
    /**
     * 重置关卡按钮点击
     */
    LevelSelectPageController.prototype.onResetButtonClick = function () {
        if (this.levelSelectController) {
            // 重置到初始状态：没有完成任何关卡，第1关为当前关卡
            this.levelSelectController.setLevelProgress(0);
            cc.log("重置所有关卡");
        }
    };
    /**
     * 进入选中的关卡
     */
    LevelSelectPageController.prototype.enterSelectedLevel = function () {
        if (this.levelSelectController) {
            var selectedLevel = this.levelSelectController.getCurrentSelectedLevel();
            cc.log("\u8FDB\u5165\u5173\u5361 " + selectedLevel);
            // 这里可以添加进入关卡的具体逻辑
            // 例如：切换到游戏场景，传递关卡参数等
        }
    };
    /**
     * 设置关卡进度（从外部调用）
     * @param completedLevels 已完成的关卡数，当前关卡会自动设置为 completedLevels + 1
     */
    LevelSelectPageController.prototype.setLevelProgress = function (completedLevels) {
        if (!this.levelSelectController)
            return;
        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(completedLevels);
        // 更新显示（LevelSelectController.setLevelProgress 会触发回调，但为了保险起见还是手动更新一次）
        this.updateCurrentLevelDisplay();
    };
    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     * @param currentLevel 当前关卡（会被忽略，自动计算为 completedLevels + 1）
     */
    LevelSelectPageController.prototype.setLevelProgressLegacy = function (completedLevels, currentLevel) {
        // 忽略 currentLevel 参数，使用新的逻辑
        this.setLevelProgress(completedLevels);
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectPageController.prototype.fixScrollViewScrollbar = function () {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }
        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    };
    /**
     * 更新游戏按钮显示状态
     */
    LevelSelectPageController.prototype.updateGameButtons = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (!levelData)
            return;
        // 根据关卡状态显示不同的按钮
        var isLocked = levelData.status === LevelSelectController_1.LevelStatus.LOCKED;
        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelSelectPageController.prototype.onStartGameButtonClick = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (levelData && levelData.status !== LevelSelectController_1.LevelStatus.LOCKED) {
            cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + currentLevel);
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    };
    /**
     * 未解锁按钮点击事件
     */
    LevelSelectPageController.prototype.onLockedButtonClick = function () {
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        cc.log("\u5173\u5361 " + currentLevel + " \u5C1A\u672A\u89E3\u9501");
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    };
    /**
     * 关卡选择变化回调
     */
    LevelSelectPageController.prototype.onLevelSelectionChanged = function (levelNumber) {
        this.updateCurrentLevelDisplay();
    };
    __decorate([
        property(LevelSelectController_1.default)
    ], LevelSelectPageController.prototype, "levelSelectController", void 0);
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectPageController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Label)
    ], LevelSelectPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "completeButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "unlockButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "resetButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "lockedButton", void 0);
    LevelSelectPageController = __decorate([
        ccclass
    ], LevelSelectPageController);
    return LevelSelectPageController;
}(cc.Component));
exports.default = LevelSelectPageController;

cc._RF.pop();