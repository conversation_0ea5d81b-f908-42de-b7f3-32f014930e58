{"version": 3, "sources": ["assets/scripts/hall/Level/LevelSelectPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,iEAA6E;AAEvE,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAuD,6CAAY;IAAnE;QAAA,qEAuNC;QApNG,2BAAqB,GAA0B,IAAI,CAAC;QAGpD,gBAAU,GAAkB,IAAI,CAAC;QAGjC,uBAAiB,GAAa,IAAI,CAAC;QAGnC,oBAAc,GAAc,IAAI,CAAC;QAGjC,kBAAY,GAAc,IAAI,CAAC;QAG/B,iBAAW,GAAc,IAAI,CAAC;QAE9B,UAAU;QAEV,qBAAe,GAAc,IAAI,CAAC;QAGlC,kBAAY,GAAc,IAAI,CAAC;;IA8LnC,CAAC;IA5LG,0CAAM,GAAN;QACI,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,SAAS;QACT,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SAC1E;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;SACtE;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SACpE;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;SACtE;IACL,CAAC;IAED,yCAAK,GAAL;QAAA,iBAUC;QATG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,aAAa;QACb,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,GAAG,UAAC,WAAmB;gBACrE,KAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACK,6DAAyB,GAAjC;QACI,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACtD,IAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YAC1E,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,2CAAW,YAAc,CAAC;SAC7D;QACD,aAAa;QACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,yDAAqB,GAA7B;QACI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,EAAE,CAAC;YAClD,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACK,uDAAmB,GAA3B;QACI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,CAAC;YAC7C,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACnB;IACL,CAAC;IAED;;OAEG;IACK,sDAAkB,GAA1B;QACI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,4BAA4B;YAC5B,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAC/C,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACI,sDAAkB,GAAzB;QACI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YAC3E,EAAE,CAAC,GAAG,CAAC,8BAAQ,aAAe,CAAC,CAAC;YAChC,kBAAkB;YAClB,qBAAqB;SACxB;IACL,CAAC;IAED;;;OAGG;IACI,oDAAgB,GAAvB,UAAwB,eAAuB;QAC3C,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QAExC,uCAAuC;QACvC,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAE7D,qEAAqE;QACrE,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACI,0DAAsB,GAA7B,UAA8B,eAAuB,EAAE,YAAoB;QACvE,4BAA4B;QAC5B,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,0DAAsB,GAA9B;QACI,qCAAqC;QACrC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC5C;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE;YACrE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;YACjE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAClE;IACL,CAAC;IAED;;OAEG;IACK,qDAAiB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QAExC,IAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;QAC1E,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAExE,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,gBAAgB;QAChB,IAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,CAAC;QAEzD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC;SAChD;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACK,0DAAsB,GAA9B;QACI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QAExC,IAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;QAC1E,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAExE,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,EAAE;YACtD,EAAE,CAAC,GAAG,CAAC,6CAAa,YAAc,CAAC,CAAC;YACpC,cAAc;YACd,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACK,uDAAmB,GAA3B;QACI,IAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;QAC1E,EAAE,CAAC,GAAG,CAAC,kBAAM,YAAY,8BAAO,CAAC,CAAC;QAClC,qBAAqB;QACrB,YAAY;IAChB,CAAC;IAED;;OAEG;IACK,2DAAuB,GAA/B,UAAgC,WAAmB;QAE/C,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAnND;QADC,QAAQ,CAAC,+BAAqB,CAAC;4EACoB;IAGpD;QADC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC;iEACS;IAGjC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;wEACgB;IAGnC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;qEACa;IAGjC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACW;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACU;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACc;IAGlC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACW;IAzBd,yBAAyB;QAD7C,OAAO;OACa,yBAAyB,CAuN7C;IAAD,gCAAC;CAvND,AAuNC,CAvNsD,EAAE,CAAC,SAAS,GAuNlE;kBAvNoB,yBAAyB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport LevelSelectController, { LevelStatus } from \"./LevelSelectController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LevelSelectPageController extends cc.Component {\n\n    @property(LevelSelectController)\n    levelSelectController: LevelSelectController = null;\n\n    @property(cc.ScrollView)\n    scrollView: cc.ScrollView = null;\n\n    @property(cc.Label)\n    currentLevelLabel: cc.Label = null;\n\n    @property(cc.Button)\n    completeButton: cc.Button = null;\n\n    @property(cc.Button)\n    unlockButton: cc.Button = null;\n\n    @property(cc.Button)\n    resetButton: cc.Button = null;\n\n    // 新增的游戏按钮\n    @property(cc.Button)\n    startGameButton: cc.Button = null;\n\n    @property(cc.Button)\n    lockedButton: cc.Button = null;\n\n    onLoad() {\n        // 修复ScrollView的Scrollbar问题\n        this.fixScrollViewScrollbar();\n\n        // 设置按钮事件\n        if (this.completeButton) {\n            this.completeButton.node.on('click', this.onCompleteButtonClick, this);\n        }\n        if (this.unlockButton) {\n            this.unlockButton.node.on('click', this.onUnlockButtonClick, this);\n        }\n        if (this.resetButton) {\n            this.resetButton.node.on('click', this.onResetButtonClick, this);\n        }\n        if (this.startGameButton) {\n            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);\n        }\n        if (this.lockedButton) {\n            this.lockedButton.node.on('click', this.onLockedButtonClick, this);\n        }\n    }\n\n    start() {\n        this.updateCurrentLevelDisplay();\n        this.updateGameButtons();\n\n        // 设置关卡选择变化回调\n        if (this.levelSelectController) {\n            this.levelSelectController.onLevelSelectionChanged = (levelNumber: number) => {\n                this.onLevelSelectionChanged(levelNumber);\n            };\n        }\n    }\n\n    /**\n     * 更新当前关卡显示\n     */\n    private updateCurrentLevelDisplay() {\n        if (this.currentLevelLabel && this.levelSelectController) {\n            const currentLevel = this.levelSelectController.getCurrentSelectedLevel();\n            this.currentLevelLabel.string = `当前选中关卡: ${currentLevel}`;\n        }\n        // 同时更新游戏按钮状态\n        this.updateGameButtons();\n    }\n\n    /**\n     * 完成当前关卡按钮点击\n     */\n    private onCompleteButtonClick() {\n        if (this.levelSelectController) {\n            this.levelSelectController.completeCurrentLevel();\n            this.updateCurrentLevelDisplay();\n            cc.log(\"完成当前关卡\");\n        }\n    }\n\n    /**\n     * 解锁下一关按钮点击\n     */\n    private onUnlockButtonClick() {\n        if (this.levelSelectController) {\n            this.levelSelectController.unlockNextLevel();\n            this.updateCurrentLevelDisplay();\n            cc.log(\"解锁下一关\");\n        }\n    }\n\n    /**\n     * 重置关卡按钮点击\n     */\n    private onResetButtonClick() {\n        if (this.levelSelectController) {\n            // 重置到初始状态：没有完成任何关卡，第1关为当前关卡\n            this.levelSelectController.setLevelProgress(0);\n            cc.log(\"重置所有关卡\");\n        }\n    }\n\n    /**\n     * 进入选中的关卡\n     */\n    public enterSelectedLevel() {\n        if (this.levelSelectController) {\n            const selectedLevel = this.levelSelectController.getCurrentSelectedLevel();\n            cc.log(`进入关卡 ${selectedLevel}`);\n            // 这里可以添加进入关卡的具体逻辑\n            // 例如：切换到游戏场景，传递关卡参数等\n        }\n    }\n\n    /**\n     * 设置关卡进度（从外部调用）\n     * @param completedLevels 已完成的关卡数，当前关卡会自动设置为 completedLevels + 1\n     */\n    public setLevelProgress(completedLevels: number) {\n        if (!this.levelSelectController) return;\n\n        // 使用 LevelSelectController 的新方法来设置关卡进度\n        this.levelSelectController.setLevelProgress(completedLevels);\n\n        // 更新显示（LevelSelectController.setLevelProgress 会触发回调，但为了保险起见还是手动更新一次）\n        this.updateCurrentLevelDisplay();\n    }\n\n    /**\n     * 设置关卡进度（兼容旧接口）\n     * @param completedLevels 已完成的关卡数\n     * @param currentLevel 当前关卡（会被忽略，自动计算为 completedLevels + 1）\n     */\n    public setLevelProgressLegacy(completedLevels: number, currentLevel: number) {\n        // 忽略 currentLevel 参数，使用新的逻辑\n        this.setLevelProgress(completedLevels);\n    }\n\n    /**\n     * 修复ScrollView的Scrollbar问题\n     */\n    private fixScrollViewScrollbar() {\n        // 如果有ScrollView引用，清除Scrollbar引用以避免错误\n        if (this.scrollView) {\n            this.scrollView.horizontalScrollBar = null;\n            this.scrollView.verticalScrollBar = null;\n        }\n\n        // 如果levelSelectController有ScrollView，也进行修复\n        if (this.levelSelectController && this.levelSelectController.scrollView) {\n            this.levelSelectController.scrollView.horizontalScrollBar = null;\n            this.levelSelectController.scrollView.verticalScrollBar = null;\n        }\n    }\n\n    /**\n     * 更新游戏按钮显示状态\n     */\n    private updateGameButtons() {\n        if (!this.levelSelectController) return;\n\n        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();\n        const levelData = this.levelSelectController.getLevelData(currentLevel);\n\n        if (!levelData) return;\n\n        // 根据关卡状态显示不同的按钮\n        const isLocked = levelData.status === LevelStatus.LOCKED;\n\n        if (this.startGameButton) {\n            this.startGameButton.node.active = !isLocked;\n        }\n\n        if (this.lockedButton) {\n            this.lockedButton.node.active = isLocked;\n        }\n    }\n\n    /**\n     * 开始游戏按钮点击事件\n     */\n    private onStartGameButtonClick() {\n        if (!this.levelSelectController) return;\n\n        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();\n        const levelData = this.levelSelectController.getLevelData(currentLevel);\n\n        if (levelData && levelData.status !== LevelStatus.LOCKED) {\n            cc.log(`开始游戏 - 关卡 ${currentLevel}`);\n            // 这里添加进入游戏的逻辑\n            this.enterSelectedLevel();\n        }\n    }\n\n    /**\n     * 未解锁按钮点击事件\n     */\n    private onLockedButtonClick() {\n        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();\n        cc.log(`关卡 ${currentLevel} 尚未解锁`);\n        // 这里可以添加提示用户关卡未解锁的逻辑\n        // 例如显示提示弹窗等\n    }\n\n    /**\n     * 关卡选择变化回调\n     */\n    private onLevelSelectionChanged(levelNumber: number) {\n      \n        this.updateCurrentLevelDisplay();\n    }\n}\n"]}