{"version": 3, "sources": ["assets/scripts/net/MessageId.ts"], "names": [], "mappings": ";;;;;;;AAAA,OAAO;AACP,IAAY,SAuDX;AAvDD,WAAY,SAAS;IACjB,yCAA4B,CAAA;IAC5B,qDAAwC,CAAA;IACxC,2CAA8B,CAAA;IAC9B,mCAAsB,CAAA;IACtB,yCAA4B,CAAA;IAC5B,+CAAkC,CAAA;IAClC,6CAAgC,CAAA;IAChC,6CAAgC,CAAA;IAChC,2CAA8B,CAAA;IAC9B,uCAA0B,CAAA;IAC1B,iDAAoC,CAAA;IACpC,mCAAsB,CAAA;IACtB,mCAAsB,CAAA;IACtB,2CAA8B,CAAA;IAC9B,+CAAkC,CAAA;IAClC,+CAAkC,CAAA;IAElC,iDAAoC,CAAA;IACpC,iDAAoC,CAAA;IACpC,+CAAkC,CAAA;IAClC,iDAAoC,CAAA;IACpC,+CAAk<PERSON>,CAAA;IAClC,6DAAgD,CAAA;IAChD,mDAAsC,CAAA;IACtC,+CAAkC,CAAA;IAElC,6CAAgC,CAAA;IAChC,2CAA8B,CAAA;IAC9B,2CAA8B,CAAA;IAC9B,iDAAoC,CAAA;IACpC,+CAAkC,CAAA;IAClC,yCAA4B,CAAA;IAC5B,2CAA8B,CAAA;IAC9B,uCAA0B,CAAA;IAC1B,6CAAgC,CAAA;IAChC,yDAA4C,CAAA;IAC5C,mDAAsC,CAAA;IACtC,iDAAoC,CAAA;IACpC,6CAAgC,CAAA;IAChC,qDAAwC,CAAA;IACxC,6CAAgC,CAAA;IAChC,uCAA0B,CAAA;IAE1B,2CAA8B,CAAA;IAC9B,yCAA4B,CAAA;IAE5B,WAAW;IACX,yDAA4C,CAAA;IAC5C,+DAAkD,CAAA;IAClD,qDAAwC,CAAA;IACxC,qEAAwD,CAAA;IACxD,mDAAsC,CAAA;IACtC,6CAAgC,CAAA;AAEpC,CAAC,EAvDW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAuDpB", "file": "", "sourceRoot": "/", "sourcesContent": ["//消息 id\r\nexport enum MessageId {\r\n    MsgTypeCreateWs = 'CreateWs',                   // 创建ws连接\r\n    MsgTypeNoticeUserCoin = 'NoticeUserCoin', // 同步玩家金币\r\n    MsgTypeHeartbeat = 'Heartbeat',           // 心跳\r\n    MsgTypeLogin = 'Login',               // 玩家登录\r\n    MsgTypeUserInfo = 'UserInfo',           // 玩家请求自己信息\r\n    MsgTypePairRequest = 'PairRequest',     // 玩家请求匹配\r\n    MsgTypeCancelPair = 'CancelPair',        // 玩家取消匹配\r\n    MsgTypePairResult = 'PairResult',       // 服务通知客户端匹配到了其他玩家\r\n    MsgTypeEnterRoom = 'EnterRoom',        // 玩家请求进入房间\r\n    MsgTypeSitDown = 'SitDown',        // 玩家请求坐下\r\n    MsgTypeRobotSitDown = 'RobotSitDown',    // 机器人请求坐下\r\n    MsgTypeStand = 'Stand',      // 玩家请求站起\r\n    MsgTypeReady = 'Ready',      // 客户端通知已经准备好\r\n    MsgTypeLeaveRoom = 'LeaveRoom',     // 玩家主动离开房间\r\n    MsgTypeUserOffline = 'UserOffline',     // 玩家离线\r\n    MsgTypeKickOutUser = 'KickOutUser',     // 玩家被踢出房间\r\n\r\n    MsgTypeCreateInvite = 'CreateInvite',           // 创建邀请\r\n    MsgTypeAcceptInvite = 'AcceptInvite',           // 接受邀请\r\n    MsgTypeInviteReady = 'InviteReady',            // 邀请者准备\r\n    MsgTypeChgInviteCfg = 'ChgInviteCfg',           // 邀请创建者更改玩法配置\r\n    MsgTypeLeaveInvite = 'LeaveInvite',            // 离开邀请\r\n    MsgTypeNoticeInviteStatus = 'NoticeInviteStatus',// 广播邀请状态\r\n    MsgTypeInviteKickOut = 'InviteKickOut',         // 邀请创建者踢出玩家\r\n    MsgTypeInviteStart = 'InviteStart',            // 邀请创建者开始游戏\r\n\r\n    MsgTypeViewerList = 'ViewerList',          // 旁观者列表\r\n    MsgTypeGameStart = 'GameStart',             // 开始游戏\r\n    MsgTypeFirstMove = 'FirstMove',            // 先手\r\n    MsgTypeFirstMoveEnd = 'FirstMoveEnd',            // 先手 动画结束\r\n    MsgTypeUserPosList = 'UserPosList',          // 玩家座位号列表\r\n    MsgTypeRollDice = 'RollDice',             // 掷骰子\r\n    MsgTypeMoveChess = 'MoveChess',           // 移动棋子\r\n    MsgTypeUseProp = 'UseProp',            // 使用道具\r\n    MsgTypeChoiceProp = 'ChoiceProp',       // 挑选道具\r\n    MsgTypeChoicePropResult = 'ChoicePropResult',// 挑选道具的结果\r\n    MsgTypeChoiceAdvance = 'ChoiceAdvance',      // 选择前进点数\r\n    MsgTypeMoveChessEnd = 'MoveChessEnd'    ,     // 移动棋子结束\r\n    MsgTypeSettlement = 'Settlement',       // 大结算\r\n    MsgTypeProductConfigs = 'ProductConfigs',       // 请求商品列表\r\n    MsgTypeBuyProduct = 'BuyProduct',       // 请求购买商品\r\n    MsgTypeSetSkin = 'SetSkin',       // 请求设置皮肤\r\n\r\n    MsgTypeMoveBlock = \"MoveBlock\",// 移动块\r\n    MsgTypeScoreChg = \"ScoreChg\",// 更新积分\r\n\r\n    // 扫雷游戏相关消息\r\n    MsgTypeNoticeRoundStart = 'NoticeRoundStart',     // 扫雷回合开始通知\r\n    MsgTypeNoticeActionDisplay = 'NoticeActionDisplay', // 扫雷操作展示通知\r\n    MsgTypeNoticeRoundEnd = 'NoticeRoundEnd',         // 扫雷回合结束通知\r\n    MsgTypeNoticeFirstChoiceBonus = 'NoticeFirstChoiceBonus', // 扫雷首选玩家奖励通知\r\n    MsgTypeNoticeGameEnd = 'NoticeGameEnd',           // 扫雷游戏结束通知\r\n    MsgTypeClickBlock = 'ClickBlock',                 // 点击方块\r\n\r\n}"]}