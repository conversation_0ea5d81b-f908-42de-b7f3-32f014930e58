"use strict";
cc._RF.push(module, 'ef3738C6EtNP63rWdv6bSAj', 'GameScoreController');
// scripts/game/GameScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var PlayerScoreController_1 = require("../pfb/PlayerScoreController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GameScoreController = /** @class */ (function (_super) {
    __extends(GameScoreController, _super);
    function GameScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scoreLayout = null; // 分数布局容器
        _this.playerScorePfb = null; // player_score_pfb 预制体
        _this._scoreControllers = []; // 分数控制器数组
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    GameScoreController.prototype.onLoad = function () {
        // 初始化时不自动创建界面，等待游戏数据
    };
    GameScoreController.prototype.start = function () {
        // 不在start中自动创建，等待外部调用
    };
    /**
     * 创建分数显示界面
     * 只使用后端传回来的真实游戏数据
     */
    GameScoreController.prototype.createScoreView = function () {
        console.log('开始创建分数显示界面');
        // 检查必要的组件是否存在
        if (!this.scoreLayout) {
            console.error("scoreLayout 未设置！请在编辑器中拖拽布局节点到 scoreLayout 属性");
            return;
        }
        if (!this.playerScorePfb) {
            console.error("playerScorePfb 未设置！请在编辑器中拖拽预制体到 playerScorePfb 属性");
            return;
        }
        // 只使用后端传回来的真实游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法创建分数界面。请等待 NoticeStartGame 消息");
            return;
        }
        // 获取后端传回来的用户数据
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        console.log("\u83B7\u53D6\u5230 " + users.length + " \u4E2A\u73A9\u5BB6\u6570\u636E");
        // 确保所有用户都有score字段，初始化为0
        users.forEach(function (user, index) {
            if (user.score === undefined || user.score === null) {
                user.score = 0;
            }
            console.log("\u73A9\u5BB6" + index + ": " + user.nickName + " (" + user.userId + "), \u5206\u6570: " + user.score);
        });
        // 清空现有的分数显示
        this.scoreLayout.removeAllChildren();
        this._scoreControllers = [];
        // 根据后端用户数据生成分数预制体
        for (var i = 0; i < users.length; i++) {
            var item = cc.instantiate(this.playerScorePfb);
            this.scoreLayout.addChild(item);
            var scoreController = item.getComponent(PlayerScoreController_1.default);
            if (scoreController) {
                this._scoreControllers.push(scoreController);
                scoreController.setData(users[i]);
                console.log("\u521B\u5EFA\u5206\u6570\u63A7\u5236\u5668 " + i + ": " + users[i].nickName);
            }
            else {
                console.error("预制体上没有找到 PlayerScoreController 组件");
            }
        }
        console.log("\u5206\u6570\u754C\u9762\u521B\u5EFA\u5B8C\u6210\uFF0C\u5171\u521B\u5EFA " + this._scoreControllers.length + " \u4E2A\u5206\u6570\u63A7\u5236\u5668");
    };
    /**
     * 初始化分数界面
     * 当收到 NoticeStartGame 消息后调用此方法
     */
    GameScoreController.prototype.initializeScoreView = function () {
        console.log('初始化分数界面');
        this.createScoreView();
    };
    /**
     * 设置游戏数据
     * 更新所有玩家的分数显示
     */
    GameScoreController.prototype.setGameData = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法设置分数数据");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        console.log("\u8BBE\u7F6E\u6E38\u620F\u6570\u636E\uFF0C\u66F4\u65B0 " + users.length + " \u4E2A\u73A9\u5BB6\u7684\u5206\u6570\u663E\u793A");
        // 更新所有玩家的分数显示
        for (var i = 0; i < users.length; i++) {
            if (i < this._scoreControllers.length) {
                this._scoreControllers[i].setData(users[i]);
                console.log("\u66F4\u65B0\u73A9\u5BB6" + i + "\u6570\u636E: " + users[i].nickName + ", \u5206\u6570: " + users[i].score);
            }
        }
    };
    /**
     * 更新特定玩家的分数
     * @param userId 玩家ID
     * @param score 新的分数
     */
    GameScoreController.prototype.updatePlayerScore = function (userId, score) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            this._scoreControllers[userIndex].updateScore(score);
            console.log("\u66F4\u65B0\u73A9\u5BB6\u5206\u6570: " + userId + ", \u65B0\u5206\u6570: " + score);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6\u6216\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex);
        }
    };
    /**
     * 更新所有玩家分数
     */
    GameScoreController.prototype.updateAllScores = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新所有玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        console.log("\u66F4\u65B0\u6240\u6709\u73A9\u5BB6\u5206\u6570\uFF0C\u5171 " + users.length + " \u4E2A\u73A9\u5BB6");
        for (var i = 0; i < users.length && i < this._scoreControllers.length; i++) {
            this._scoreControllers[i].updateScore(users[i].score || 0);
            console.log("\u66F4\u65B0\u73A9\u5BB6" + i + "\u5206\u6570: " + users[i].nickName + ", \u5206\u6570: " + (users[i].score || 0));
        }
    };
    /**
     * 获取指定索引的PlayerScoreController
     * @param userIndex 用户索引
     * @returns PlayerScoreController 或 null
     */
    GameScoreController.prototype.getPlayerScoreController = function (userIndex) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            return this._scoreControllers[userIndex];
        }
        return null;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GameScoreController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        console.log('收到首选玩家奖励通知:', data);
        console.log("\u56DE\u5408: " + data.roundNumber + ", \u73A9\u5BB6: " + data.userId + ", \u5956\u52B1\u5206\u6570: +" + data.bonusScore + ", \u603B\u5206: " + data.totalScore);
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理首选玩家奖励");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === data.userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            console.log("\u627E\u5230\u7528\u6237\u7D22\u5F15: " + userIndex + ", \u7528\u6237ID: " + data.userId);
            // 1. 更新玩家的总分数到全局数据
            users[userIndex].score = data.totalScore;
            console.log("\u66F4\u65B0\u5168\u5C40\u7528\u6237\u5206\u6570: " + data.totalScore);
            // 2. 更新分数显示 - 显示新的总分
            this._scoreControllers[userIndex].updateScore(data.totalScore);
            console.log("\u66F4\u65B0\u5206\u6570\u663E\u793A\u5B8C\u6210: " + data.totalScore);
            // 3. 显示加分效果动画 - 显示奖励分数
            this.showAddScoreWithAnimation(userIndex, data.bonusScore);
            console.log("\u663E\u793A\u52A0\u5206\u6548\u679C: +" + data.bonusScore);
            // 4. 判断是否为当前用户，如果是则同时更新player_game_pfb
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isMyself = (data.userId === currentUserId);
            if (isMyself) {
                // 更新对应的player_game_pfb中的change_score
                this.updatePlayerGameScore(data.userId, data.bonusScore);
                console.log("\u81EA\u5DF1\u83B7\u5F97\u9996\u9009\u5956\u52B1 +" + data.bonusScore + "\uFF0C\u603B\u5206: " + data.totalScore);
            }
            else {
                console.log("\u5176\u4ED6\u73A9\u5BB6\u83B7\u5F97\u9996\u9009\u5956\u52B1: " + data.userId + ", +" + data.bonusScore + "\uFF0C\u603B\u5206: " + data.totalScore);
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + data.userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
            // 打印所有用户信息用于调试
            console.log('所有用户信息:');
            users.forEach(function (user, index) {
                console.log("  [" + index + "] userId: " + user.userId + ", nickName: " + user.nickName);
            });
        }
    };
    /**
     * 显示加分效果动画
     * @param userIndex 用户索引
     * @param bonusScore 奖励分数
     */
    GameScoreController.prototype.showAddScoreWithAnimation = function (userIndex, bonusScore) {
        console.log("\u663E\u793A\u52A0\u5206\u52A8\u753B: userIndex=" + userIndex + ", bonusScore=+" + bonusScore);
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            var scoreController = this._scoreControllers[userIndex];
            // 调用PlayerScoreController的showAddScore方法显示加分效果
            // 这会在player_score_pfb的addscore/change_score中显示"+1"等文本
            scoreController.showAddScore(bonusScore);
            console.log("\u52A0\u5206\u52A8\u753B\u5DF2\u89E6\u53D1: +" + bonusScore);
        }
        else {
            console.warn("\u65E0\u6548\u7684\u7528\u6237\u7D22\u5F15: " + userIndex + ", \u63A7\u5236\u5668\u6570\u91CF: " + this._scoreControllers.length);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GameScoreController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        console.log("\u5C1D\u8BD5\u66F4\u65B0player_game_pfb\u4E2D\u7684change_score: userId=" + userId + ", bonus=+" + bonusScore);
        // 通过GamePageController来调用ChessBoardController
        // 这里暂时不直接调用，因为GameScoreController不应该直接依赖ChessBoardController
        // 实际的调用在GamePageController.updatePlayerGameScore中完成
        console.log("player_game_pfb\u66F4\u65B0\u903B\u8F91\u7531GamePageController\u5904\u7406");
    };
    __decorate([
        property(cc.Node)
    ], GameScoreController.prototype, "scoreLayout", void 0);
    __decorate([
        property(cc.Prefab)
    ], GameScoreController.prototype, "playerScorePfb", void 0);
    GameScoreController = __decorate([
        ccclass
    ], GameScoreController);
    return GameScoreController;
}(cc.Component));
exports.default = GameScoreController;

cc._RF.pop();