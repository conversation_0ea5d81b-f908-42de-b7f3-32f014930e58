"use strict";
cc._RF.push(module, 'aba46KnCk5KrY4mF3h25sUm', 'LevelSelectTest');
// scripts/hall/Level/LevelSelectTest.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择测试脚本
 * 这个脚本专门用于测试关卡选择功能，解决ScrollView配置问题
 */
var LevelSelectTest = /** @class */ (function (_super) {
    __extends(LevelSelectTest, _super);
    function LevelSelectTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 25;
        _this.levelItemWidth = 150;
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // UI组件
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        return _this;
    }
    LevelSelectTest.prototype.onLoad = function () {
        this.createCompleteUI();
        this.initLevelData();
        this.createLevelItems();
        this.setupScrollEvents();
    };
    LevelSelectTest.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    LevelSelectTest.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 创建完整的UI结构
     */
    LevelSelectTest.prototype.createCompleteUI = function () {
        // 设置画布背景色
        this.node.color = cc.Color.BLACK;
        // 创建标题
        var titleNode = new cc.Node("Title");
        var titleLabel = titleNode.addComponent(cc.Label);
        titleLabel.string = "关卡选择测试";
        titleLabel.fontSize = 36;
        titleLabel.node.color = cc.Color.WHITE;
        titleNode.setPosition(0, 400);
        titleNode.parent = this.node;
        // 使用ScrollViewHelper创建ScrollView
        var scrollViewData = ScrollViewHelper_1.ScrollViewHelper.createHorizontalScrollView(this.node, cc.size(650, 150));
        this.scrollView = scrollViewData.scrollView;
        this.content = scrollViewData.content;
        // 设置ScrollView位置
        scrollViewData.scrollView.node.setPosition(0, 0);
        // 创建信息标签
        var infoNode = new cc.Node("InfoLabel");
        this.infoLabel = infoNode.addComponent(cc.Label);
        this.infoLabel.string = "当前选中: 关卡1 (进行中)";
        this.infoLabel.fontSize = 24;
        this.infoLabel.node.color = cc.Color.WHITE;
        infoNode.setPosition(0, -150);
        infoNode.parent = this.node;
        // 创建控制按钮
        this.createControlButtons();
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectTest.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectTest.prototype.onScrolling = function () {
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectTest.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectTest.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    /**
     * 创建控制按钮
     */
    LevelSelectTest.prototype.createControlButtons = function () {
        var _this = this;
        var buttonY = -250;
        var buttonSpacing = 150;
        // 完成关卡按钮
        this.createButton("完成关卡", cc.v2(-buttonSpacing, buttonY), function () {
            _this.completeCurrentLevel();
        });
        // 重置按钮
        this.createButton("重置", cc.v2(0, buttonY), function () {
            _this.resetLevels();
        });
        // 随机进度按钮
        this.createButton("随机进度", cc.v2(buttonSpacing, buttonY), function () {
            _this.setRandomProgress();
        });
        // 调试按钮
        this.createButton("调试信息", cc.v2(-75, buttonY - 60), function () {
            _this.debugScrollView();
        });
        // 参数调试按钮
        this.createButton("调试参数", cc.v2(75, buttonY - 60), function () {
            _this.debugParameters();
        });
    };
    /**
     * 创建按钮
     */
    LevelSelectTest.prototype.createButton = function (text, position, callback) {
        var btnNode = new cc.Node("Button_" + text);
        btnNode.setContentSize(120, 40);
        btnNode.setPosition(position);
        btnNode.parent = this.node;
        // 添加背景
        var sprite = btnNode.addComponent(cc.Sprite);
        btnNode.color = cc.Color.BLUE;
        // 添加文字
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = text;
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = btnNode;
        // 添加按钮组件
        var button = btnNode.addComponent(cc.Button);
        button.target = btnNode;
        // 设置点击事件
        btnNode.on('click', callback, this);
        return btnNode;
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectTest.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED;
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡项目
     */
    LevelSelectTest.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is null!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectTest.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectTest.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectTest.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        node.color = cc.Color.WHITE;
        // 尝试加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
            else {
            }
        });
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectTest.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 根据状态和是否选中确定图片路径和大小
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green";
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小，确保不被图片原始大小覆盖
                node.setContentSize(size);
            }
            else {
                // 如果图片加载失败，使用颜色作为备选方案
                var color = cc.Color.GRAY;
                switch (levelData.status) {
                    case LevelSelectController_1.LevelStatus.LOCKED:
                        color = cc.Color.GRAY;
                        break;
                    case LevelSelectController_1.LevelStatus.CURRENT:
                        color = cc.Color.YELLOW;
                        break;
                    case LevelSelectController_1.LevelStatus.COMPLETED:
                        color = cc.Color.GREEN;
                        break;
                }
                node.color = color;
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectTest.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectTest.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels || !this.scrollView)
            return;
        var targetIndex = levelNumber - 1;
        var percent = targetIndex / (this.totalLevels - 1);
        ScrollViewHelper_1.ScrollViewHelper.scrollToHorizontalPercent(this.scrollView, percent, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectTest.prototype.onLevelClicked = function (levelNumber) {
        // 允许选择任何关卡（包括未解锁的）
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
    };
    /**
     * 更新信息显示
     */
    LevelSelectTest.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectTest.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    /**
     * 重置关卡
     */
    LevelSelectTest.prototype.resetLevels = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i === 0) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = 1;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(1);
        this.updateInfoDisplay();
    };
    /**
     * 设置随机进度
     */
    LevelSelectTest.prototype.setRandomProgress = function () {
        var completedLevels = Math.floor(Math.random() * 10) + 1;
        var currentLevel = Math.min(completedLevels + 1, this.totalLevels);
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i < completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else if (i === completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = currentLevel;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(currentLevel);
        this.updateInfoDisplay();
    };
    /**
     * 调试ScrollView信息
     */
    LevelSelectTest.prototype.debugScrollView = function () {
        if (this.scrollView) {
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectTest");
        }
        else {
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectTest.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 动态调整连接线间距（用于调试）
     */
    LevelSelectTest.prototype.setLineSpacing = function (spacing) {
        this.lineSpacing = spacing;
        // 重新创建关卡项目以应用新参数
        this.createLevelItems();
        this.updateAllLevelsDisplay();
    };
    /**
     * 动态调整关卡到连接线距离（用于调试）
     */
    LevelSelectTest.prototype.setLevelToLineDistance = function (distance) {
        this.levelToLineDistance = distance;
        // 重新创建关卡项目以应用新参数
        this.createLevelItems();
        this.updateAllLevelsDisplay();
    };
    /**
     * 动态调整连接线数量（用于调试）
     */
    LevelSelectTest.prototype.setLineCount = function (count) {
        this.lineCount = count;
        // 重新创建关卡项目以应用新参数
        this.createLevelItems();
        this.updateAllLevelsDisplay();
    };
    /**
     * 更新标签外边框
     */
    LevelSelectTest.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    LevelSelectTest = __decorate([
        ccclass
    ], LevelSelectTest);
    return LevelSelectTest;
}(cc.Component));
exports.default = LevelSelectTest;

cc._RF.pop();