{"version": 3, "sources": ["assets/scripts/hall/Level/LevelSelectTest.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,iEAAsD;AACtD,uDAAsD;AAEhD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C;;;GAGG;AAEH;IAA6C,mCAAY;IAAzD;QAAA,qEA8qBC;QA5qBG,OAAO;QACC,mBAAa,GAAU,EAAE,CAAC;QAC1B,0BAAoB,GAAW,CAAC,CAAC;QACjC,iBAAW,GAAW,EAAE,CAAC;QACzB,oBAAc,GAAW,GAAG,CAAC;QAErC,iBAAiB;QACV,eAAS,GAAW,CAAC,CAAC,CAAC,gBAAgB;QACvC,iBAAW,GAAW,EAAE,CAAC,CAAC,WAAW;QACrC,yBAAmB,GAAW,CAAC,CAAC,CAAC,YAAY;QAEpD,OAAO;QACC,gBAAU,GAAkB,IAAI,CAAC;QACjC,aAAO,GAAY,IAAI,CAAC;QACxB,eAAS,GAAa,IAAI,CAAC;QAC3B,gBAAU,GAAc,EAAE,CAAC;QAEnC,SAAS;QACD,qBAAe,GAAY,KAAK,CAAC;;IA0pB7C,CAAC;IAxpBG,gCAAM,GAAN;QAEI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,+BAAK,GAAL;QAEI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,mCAAS,GAAT;QACI,UAAU;QACV,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;OAEG;IACK,0CAAgB,GAAxB;QACI,UAAU;QACV,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAEjC,OAAO;QACP,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACpD,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,UAAU,CAAC,QAAQ,GAAG,EAAE,CAAC;QACzB,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QACvC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9B,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAE7B,iCAAiC;QACjC,IAAM,cAAc,GAAG,mCAAgB,CAAC,0BAA0B,CAC9D,IAAI,CAAC,IAAI,EACT,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CACpB,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;QAC5C,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QAEtC,iBAAiB;QACjB,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjD,SAAS;QACT,IAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,iBAAiB,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAC3C,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9B,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAE5B,SAAS;QACT,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAGhC,CAAC;IAED;;OAEG;IACK,2CAAiB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,UAAU;QACV,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAE7D,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IAGtE,CAAC;IAED;;OAEG;IACK,qCAAW,GAAnB;QACI,IAAI,CAAC,6BAA6B,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,uCAAa,GAArB;QACI,kBAAkB;QAClB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,mBAAmB;QACnB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,uDAA6B,GAArC;QACI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE9C,qCAAqC;QACrC,IAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,qBAAqB;QAElD,yBAAyB;QACzB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAErC,6BAA6B;YAC7B,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC7E,IAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAEpF,uBAAuB;YACvB,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC;YAEpE,IAAI,QAAQ,GAAG,WAAW,EAAE;gBACxB,WAAW,GAAG,QAAQ,CAAC;gBACvB,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;aACxB;SACJ;QAED,mBAAmB;QACnB,IAAI,YAAY,KAAK,IAAI,CAAC,oBAAoB,EAAE;YAC5C,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;YACzC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAE5B;IACL,CAAC;IAED;;OAEG;IACK,8CAAoB,GAA5B;QAAA,iBA4BC;QA3BG,IAAM,OAAO,GAAG,CAAC,GAAG,CAAC;QACrB,IAAM,aAAa,GAAG,GAAG,CAAC;QAE1B,SAAS;QACT,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE;YACtD,KAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;YACvC,KAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE;YACrD,KAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;YAChD,KAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;YAC/C,KAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,sCAAY,GAApB,UAAqB,IAAY,EAAE,QAAiB,EAAE,QAAkB;QACpE,IAAM,OAAO,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAChC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC9B,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAE3B,OAAO;QACP,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QAE9B,OAAO;QACP,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAClC,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC;QAE3B,SAAS;QACT,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC;QAExB,SAAS;QACT,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEpC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,uCAAa,GAArB;QACI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,MAAM,SAAa,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;aAChC;iBAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBACf,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC;aAClC;iBAAM;gBACH,MAAM,GAAG,mCAAW,CAAC,MAAM,CAAC;aAC/B;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,WAAW,EAAE,CAAC;gBACd,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;SACN;IAEL,CAAC;IAED;;OAEG;IACK,0CAAgB,GAAxB;QACI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAClC,OAAO;SACV;QAED,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,QAAQ;QACR,IAAM,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;QAIhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAExC,SAAS;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEhC,OAAO;YACP,IAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAChE,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAE/B,kBAAkB;YAClB,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aACvC;SACJ;IAEL,CAAC;IAED;;OAEG;IACK,yCAAe,GAAvB,UAAwB,SAAc;QAAtC,iBAqCC;QApCG,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,WAAS,SAAS,CAAC,WAAa,CAAC,CAAC;QAE3D,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE5C,kBAAkB;QAClB,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAEhD,SAAS;QACT,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU;QACtD,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC;QACxD,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;QAEpD,QAAQ;QACR,IAAM,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAEnD,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;QACxB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO;QAEpC,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAErB,SAAS;QACT,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;YACb,KAAI,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,+CAAqB,GAA7B,UAA8B,UAAkB,EAAE,SAAiB;QAC/D,IAAM,MAAM,GAAG,SAAS,GAAG,EAAE,GAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,aAAa;QACzE,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QAC/D,IAAM,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,cAAc,GAAG,EAAE,GAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,gBAAgB;QAChG,IAAM,cAAc,GAAG,IAAI,GAAG,MAAM,CAAC;QAErC,qBAAqB;QACrB,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,cAAc,GAAG,cAAc,EAAE;YACjC,aAAa,GAAG,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;SACzD;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEhC,IAAM,KAAK,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC;YACzC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACK,8CAAoB,GAA5B;QACI,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE5C,cAAc;QACd,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAG5B,YAAY;QACZ,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,GAAG,EAAE,WAAW;YACnF,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE;gBACrB,MAAM,CAAC,WAAW,GAAG,WAA6B,CAAC;gBACnD,+BAA+B;gBAC/B,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAE7B;iBAAM;aAEN;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,mDAAyB,GAAjC,UAAkC,IAAa,EAAE,SAAc,EAAE,UAAmB;QAChF,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE3B,qBAAqB;QACrB,IAAI,UAAU,EAAE;YACZ,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACvB,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,yCAAyC,CAAC;oBACtD,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,2CAA2C,CAAC;oBACxD,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,0CAA0C,CAAC;oBACvD,MAAM;aACb;SACJ;aAAM;YACH,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,kCAAkC,CAAC;oBAC/C,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,oCAAoC,CAAC;oBACjD,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,mCAAmC,CAAC;oBAChD,MAAM;aACb;SACJ;QAED,SAAS;QACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAK1B,UAAU;QACV,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,GAAG,EAAE,WAAW;YAC1D,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE;gBACrB,MAAM,CAAC,WAAW,GAAG,WAA6B,CAAC;gBACnD,2BAA2B;gBAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aAE7B;iBAAM;gBACH,sBAAsB;gBACtB,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC1B,QAAQ,SAAS,CAAC,MAAM,EAAE;oBACtB,KAAK,mCAAW,CAAC,MAAM;wBACnB,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;wBACtB,MAAM;oBACV,KAAK,mCAAW,CAAC,OAAO;wBACpB,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;wBACxB,MAAM;oBACV,KAAK,mCAAW,CAAC,SAAS;wBACtB,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;wBACvB,MAAM;iBACb;gBACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aAEtB;QACL,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,SAAS,EAAE;YACX,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,KAAK,EAAE;gBACP,IAAM,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,OAAO,EAAE;oBACT,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;iBACtD;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACK,gDAAsB,GAA9B;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACxC,IAAM,UAAU,GAAG,CAAC,SAAS,CAAC,WAAW,KAAK,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAEzE,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;SAC/D;IACL,CAAC;IAED;;OAEG;IACK,uCAAa,GAArB,UAAsB,WAAmB;QACrC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAElF,IAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;QACpC,IAAM,OAAO,GAAG,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QAErD,mCAAgB,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAG9E,CAAC;IAED;;OAEG;IACK,wCAAc,GAAtB,UAAuB,WAAmB;QACtC,mBAAmB;QACnB,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;QACxC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAG7B,CAAC;IAED;;OAEG;IACK,2CAAiB,GAAzB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;YACpE,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;aACb;YACD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,2CAAW,IAAI,CAAC,oBAAoB,UAAK,UAAU,MAAG,CAAC;SAClF;IACL,CAAC;IAED;;OAEG;IACK,8CAAoB,GAA5B;QACI,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,OAAO,EAAE;YAC1C,SAAS,CAAC,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC;YAEzC,QAAQ;YACR,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,EAAE;gBAC9C,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACpE,IAAI,aAAa,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,EAAE;oBAC7C,aAAa,CAAC,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;iBAC9C;aACJ;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAE5B;IACL,CAAC;IAED;;OAEG;IACK,qCAAW,GAAnB;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;aACtD;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,MAAM,CAAC;aACrD;SACJ;QACD,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAE7B,CAAC;IAED;;OAEG;IACK,2CAAiB,GAAzB;QACI,IAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QAC3D,IAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,GAAG,eAAe,EAAE;gBACrB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC;aACxD;iBAAM,IAAI,CAAC,KAAK,eAAe,EAAE;gBAC9B,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;aACtD;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,MAAM,CAAC;aACrD;SACJ;QAED,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;QACzC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAE7B,CAAC;IAED;;OAEG;IACK,yCAAe,GAAvB;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,mCAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;SACxE;aAAM;SAEN;IACL,CAAC;IAED;;OAEG;IACK,yCAAe,GAAvB;QAGI,WAAW;QACX,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QAG/D,SAAS;QACT,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;QAGjF,IAAI,cAAc,GAAG,cAAc,EAAE;YACjC,IAAM,eAAe,GAAG,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;SAEjE;aAAM;SAEN;IAGL,CAAC;IAED;;OAEG;IACI,wCAAc,GAArB,UAAsB,OAAe;QACjC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAE3B,iBAAiB;QACjB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,gDAAsB,GAA7B,UAA8B,QAAgB;QAC1C,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QAEpC,iBAAiB;QACjB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,sCAAY,GAAnB,UAAoB,KAAa;QAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,iBAAiB;QACjB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,4CAAkB,GAA1B,UAA2B,OAAwB,EAAE,MAAmB;QACpE,IAAI,YAAsB,CAAC;QAC3B,QAAQ,MAAM,EAAE;YACZ,KAAK,mCAAW,CAAC,MAAM;gBACnB,iBAAiB;gBACjB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,mCAAW,CAAC,OAAO;gBACpB,oBAAoB;gBACpB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;gBACpC,MAAM;YACV,KAAK,mCAAW,CAAC,SAAS;gBACtB,iBAAiB;gBACjB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;gBACrC,MAAM;YACV;gBACI,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,MAAM;SACb;QAED,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC;QAC7B,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;IACtB,CAAC;IA7qBgB,eAAe;QADnC,OAAO;OACa,eAAe,CA8qBnC;IAAD,sBAAC;CA9qBD,AA8qBC,CA9qB4C,EAAE,CAAC,SAAS,GA8qBxD;kBA9qBoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { LevelStatus } from \"./LevelSelectController\";\nimport { ScrollViewHelper } from \"./ScrollViewHelper\";\n\nconst { ccclass, property } = cc._decorator;\n\n/**\n * 关卡选择测试脚本\n * 这个脚本专门用于测试关卡选择功能，解决ScrollView配置问题\n */\n@ccclass\nexport default class LevelSelectTest extends cc.Component {\n\n    // 关卡数据\n    private levelDataList: any[] = [];\n    private currentSelectedLevel: number = 1;\n    private totalLevels: number = 25;\n    private levelItemWidth: number = 150;\n\n    // 连接线配置（公开，方便调试）\n    public lineCount: number = 9; // 每两个关卡之间的连接线数量\n    public lineSpacing: number = 16; // 连接线之间的间距\n    public levelToLineDistance: number = 8; // 关卡到连接线的距离\n\n    // UI组件\n    private scrollView: cc.ScrollView = null;\n    private content: cc.Node = null;\n    private infoLabel: cc.Label = null;\n    private levelNodes: cc.Node[] = [];\n\n    // 滑动状态标志\n    private isAutoScrolling: boolean = false;\n\n    onLoad() {\n\n        this.createCompleteUI();\n        this.initLevelData();\n        this.createLevelItems();\n        this.setupScrollEvents();\n    }\n\n    start() {\n      \n        this.scrollToLevel(this.currentSelectedLevel);\n        this.updateInfoDisplay();\n    }\n\n    onDestroy() {\n        // 移除事件监听器\n        if (this.scrollView) {\n            this.scrollView.node.off('scrolling', this.onScrolling, this);\n            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);\n        }\n    }\n\n    /**\n     * 创建完整的UI结构\n     */\n    private createCompleteUI() {\n        // 设置画布背景色\n        this.node.color = cc.Color.BLACK;\n\n        // 创建标题\n        const titleNode = new cc.Node(\"Title\");\n        const titleLabel = titleNode.addComponent(cc.Label);\n        titleLabel.string = \"关卡选择测试\";\n        titleLabel.fontSize = 36;\n        titleLabel.node.color = cc.Color.WHITE;\n        titleNode.setPosition(0, 400);\n        titleNode.parent = this.node;\n\n        // 使用ScrollViewHelper创建ScrollView\n        const scrollViewData = ScrollViewHelper.createHorizontalScrollView(\n            this.node, \n            cc.size(650, 150)\n        );\n        \n        this.scrollView = scrollViewData.scrollView;\n        this.content = scrollViewData.content;\n        \n        // 设置ScrollView位置\n        scrollViewData.scrollView.node.setPosition(0, 0);\n\n        // 创建信息标签\n        const infoNode = new cc.Node(\"InfoLabel\");\n        this.infoLabel = infoNode.addComponent(cc.Label);\n        this.infoLabel.string = \"当前选中: 关卡1 (进行中)\";\n        this.infoLabel.fontSize = 24;\n        this.infoLabel.node.color = cc.Color.WHITE;\n        infoNode.setPosition(0, -150);\n        infoNode.parent = this.node;\n\n        // 创建控制按钮\n        this.createControlButtons();\n\n      \n    }\n\n    /**\n     * 设置滑动事件监听\n     */\n    private setupScrollEvents() {\n        if (!this.scrollView) return;\n\n        // 监听滑动中事件\n        this.scrollView.node.on('scrolling', this.onScrolling, this);\n\n        // 监听滑动结束事件\n        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);\n\n    \n    }\n\n    /**\n     * 滑动中事件处理\n     */\n    private onScrolling() {\n        this.updateSelectedLevelByPosition();\n    }\n\n    /**\n     * 滑动结束事件处理\n     */\n    private onScrollEnded() {\n        // 如果是自动滚动触发的事件，忽略\n        if (this.isAutoScrolling) {\n            this.isAutoScrolling = false;\n            return;\n        }\n\n        this.updateSelectedLevelByPosition();\n        // 滑动结束后，将选中的关卡固定居中\n        this.isAutoScrolling = true;\n        this.scrollToLevel(this.currentSelectedLevel);\n    }\n\n    /**\n     * 根据当前位置更新选中的关卡\n     */\n    private updateSelectedLevelByPosition() {\n        if (!this.scrollView || !this.content) return;\n\n        // 获取ScrollView的中心位置（相对于ScrollView节点）\n        const scrollViewCenterX = 0; // ScrollView的中心就是x=0\n\n        // 找到最接近ScrollView中心位置的关卡\n        let closestLevel = 1;\n        let minDistance = Number.MAX_VALUE;\n\n        for (let i = 0; i < this.levelNodes.length; i++) {\n            const levelNode = this.levelNodes[i];\n\n            // 将关卡节点的本地坐标转换为ScrollView坐标系\n            const levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);\n            const levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);\n\n            // 计算关卡与ScrollView中心的距离\n            const distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);\n\n            if (distance < minDistance) {\n                minDistance = distance;\n                closestLevel = i + 1;\n            }\n        }\n\n        // 如果选中的关卡发生变化，更新显示\n        if (closestLevel !== this.currentSelectedLevel) {\n            this.currentSelectedLevel = closestLevel;\n            this.updateAllLevelsDisplay();\n            this.updateInfoDisplay();\n         \n        }\n    }\n\n    /**\n     * 创建控制按钮\n     */\n    private createControlButtons() {\n        const buttonY = -250;\n        const buttonSpacing = 150;\n\n        // 完成关卡按钮\n        this.createButton(\"完成关卡\", cc.v2(-buttonSpacing, buttonY), () => {\n            this.completeCurrentLevel();\n        });\n\n        // 重置按钮\n        this.createButton(\"重置\", cc.v2(0, buttonY), () => {\n            this.resetLevels();\n        });\n\n        // 随机进度按钮\n        this.createButton(\"随机进度\", cc.v2(buttonSpacing, buttonY), () => {\n            this.setRandomProgress();\n        });\n\n        // 调试按钮\n        this.createButton(\"调试信息\", cc.v2(-75, buttonY - 60), () => {\n            this.debugScrollView();\n        });\n\n        // 参数调试按钮\n        this.createButton(\"调试参数\", cc.v2(75, buttonY - 60), () => {\n            this.debugParameters();\n        });\n    }\n\n    /**\n     * 创建按钮\n     */\n    private createButton(text: string, position: cc.Vec2, callback: Function): cc.Node {\n        const btnNode = new cc.Node(\"Button_\" + text);\n        btnNode.setContentSize(120, 40);\n        btnNode.setPosition(position);\n        btnNode.parent = this.node;\n\n        // 添加背景\n        const sprite = btnNode.addComponent(cc.Sprite);\n        btnNode.color = cc.Color.BLUE;\n\n        // 添加文字\n        const labelNode = new cc.Node(\"Label\");\n        const label = labelNode.addComponent(cc.Label);\n        label.string = text;\n        label.fontSize = 16;\n        label.node.color = cc.Color.WHITE;\n        labelNode.parent = btnNode;\n\n        // 添加按钮组件\n        const button = btnNode.addComponent(cc.Button);\n        button.target = btnNode;\n\n        // 设置点击事件\n        btnNode.on('click', callback, this);\n\n        return btnNode;\n    }\n\n    /**\n     * 初始化关卡数据\n     */\n    private initLevelData() {\n        this.levelDataList = [];\n        for (let i = 1; i <= this.totalLevels; i++) {\n            let status: LevelStatus;\n            if (i === 1) {\n                status = LevelStatus.CURRENT;\n            } else if (i <= 3) {\n                status = LevelStatus.COMPLETED;\n            } else {\n                status = LevelStatus.LOCKED;\n            }\n\n            this.levelDataList.push({\n                levelNumber: i,\n                status: status\n            });\n        }\n\n    }\n\n    /**\n     * 创建关卡项目\n     */\n    private createLevelItems() {\n        if (!this.content) {\n            cc.error(\"Content node is null!\");\n            return;\n        }\n\n        // 清空现有内容\n        this.content.removeAllChildren();\n        this.levelNodes = [];\n\n        // 计算总宽度\n        const totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;\n        this.content.width = totalWidth;\n\n\n\n        for (let i = 0; i < this.totalLevels; i++) {\n            const levelData = this.levelDataList[i];\n            \n            // 创建关卡节点\n            const levelNode = this.createLevelNode(levelData);\n            this.content.addChild(levelNode);\n            this.levelNodes.push(levelNode);\n\n            // 设置位置\n            const posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;\n            levelNode.setPosition(posX, 0);\n\n            // 创建连接线（除了最后一个关卡）\n            if (i < this.totalLevels - 1) {\n                this.createConnectionLines(i, posX);\n            }\n        }\n\n    }\n\n    /**\n     * 创建关卡节点\n     */\n    private createLevelNode(levelData: any): cc.Node {\n        const node = new cc.Node(`Level_${levelData.levelNumber}`);\n        \n        // 添加Sprite组件\n        const sprite = node.addComponent(cc.Sprite);\n        \n        // 添加Label组件显示关卡数字\n        const labelNode = new cc.Node(\"LevelLabel\");\n        const label = labelNode.addComponent(cc.Label);\n        label.string = levelData.levelNumber.toString();\n\n        // 设置字体样式\n        label.fontSize = 30;\n        label.node.color = cc.color(255, 255, 255); // #FFFFFF\n        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;\n        label.verticalAlign = cc.Label.VerticalAlign.CENTER;\n\n        // 添加外边框\n        const outline = label.addComponent(cc.LabelOutline);\n        this.updateLabelOutline(outline, levelData.status);\n\n        labelNode.parent = node;\n        labelNode.setPosition(0, 0); // 居中对齐\n\n        // 添加Button组件\n        const button = node.addComponent(cc.Button);\n        button.target = node;\n\n        // 设置点击事件\n        node.on('click', () => {\n            this.onLevelClicked(levelData.levelNumber);\n        }, this);\n\n        // 更新关卡外观\n        this.updateLevelNodeAppearance(node, levelData, false);\n\n        return node;\n    }\n\n    /**\n     * 创建连接线组（9个连接线）\n     */\n    private createConnectionLines(levelIndex: number, levelPosX: number) {\n        const startX = levelPosX + 46/2 + this.levelToLineDistance; // 关卡右边缘 + 距离\n        const totalLineWidth = (this.lineCount - 1) * this.lineSpacing;\n        const endX = levelPosX + this.levelItemWidth - 46/2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离\n        const availableWidth = endX - startX;\n\n        // 如果可用宽度小于需要的宽度，调整间距\n        let actualSpacing = this.lineSpacing;\n        if (totalLineWidth > availableWidth) {\n            actualSpacing = availableWidth / (this.lineCount - 1);\n        }\n\n        for (let i = 0; i < this.lineCount; i++) {\n            const lineNode = this.createSingleLineNode();\n            this.content.addChild(lineNode);\n\n            const lineX = startX + i * actualSpacing;\n            lineNode.setPosition(lineX, 0);\n        }\n    }\n\n    /**\n     * 创建单个连接线节点\n     */\n    private createSingleLineNode(): cc.Node {\n        const node = new cc.Node(\"Line\");\n        const sprite = node.addComponent(cc.Sprite);\n\n        // 设置连接线大小为6*6\n        node.setContentSize(6, 6);\n        node.color = cc.Color.WHITE;\n\n\n        // 尝试加载连接线图片\n        cc.resources.load(\"hall_page_res/Level_Btn/pop_line\", cc.SpriteFrame, (err, spriteFrame) => {\n            if (!err && spriteFrame) {\n                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;\n                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖\n                node.setContentSize(6, 6);\n               \n            } else {\n      \n            }\n        });\n\n        return node;\n    }\n\n    /**\n     * 更新关卡节点外观\n     */\n    private updateLevelNodeAppearance(node: cc.Node, levelData: any, isSelected: boolean) {\n        const sprite = node.getComponent(cc.Sprite);\n        if (!sprite) return;\n\n        let imagePath = \"\";\n        let size = cc.size(46, 46);\n\n        // 根据状态和是否选中确定图片路径和大小\n        if (isSelected) {\n            size = cc.size(86, 86);\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_gray_choose\";\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = \"hall_page_res/Level_Btn/pop_yellow_choose\";\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_green_choose\";\n                    break;\n            }\n        } else {\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_gray\";\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = \"hall_page_res/Level_Btn/pop_yellow\";\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_green\";\n                    break;\n            }\n        }\n\n        // 设置节点大小\n        node.setContentSize(size);\n\n  \n      \n\n        // 加载并设置图片\n        cc.resources.load(imagePath, cc.SpriteFrame, (err, spriteFrame) => {\n            if (!err && spriteFrame) {\n                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;\n                // 图片加载后重新设置大小，确保不被图片原始大小覆盖\n                node.setContentSize(size);\n                \n            } else {\n                // 如果图片加载失败，使用颜色作为备选方案\n                let color = cc.Color.GRAY;\n                switch (levelData.status) {\n                    case LevelStatus.LOCKED:\n                        color = cc.Color.GRAY;\n                        break;\n                    case LevelStatus.CURRENT:\n                        color = cc.Color.YELLOW;\n                        break;\n                    case LevelStatus.COMPLETED:\n                        color = cc.Color.GREEN;\n                        break;\n                }\n                node.color = color;\n                \n            }\n        });\n\n        // 更新标签外边框\n        const labelNode = node.getChildByName(\"LevelLabel\");\n        if (labelNode) {\n            const label = labelNode.getComponent(cc.Label);\n            if (label) {\n                const outline = label.getComponent(cc.LabelOutline);\n                if (outline) {\n                    this.updateLabelOutline(outline, levelData.status);\n                }\n            }\n        }\n    }\n\n    /**\n     * 更新所有关卡显示\n     */\n    private updateAllLevelsDisplay() {\n        for (let i = 0; i < this.levelNodes.length; i++) {\n            const node = this.levelNodes[i];\n            const levelData = this.levelDataList[i];\n            const isSelected = (levelData.levelNumber === this.currentSelectedLevel);\n            \n            this.updateLevelNodeAppearance(node, levelData, isSelected);\n        }\n    }\n\n    /**\n     * 滚动到指定关卡\n     */\n    private scrollToLevel(levelNumber: number) {\n        if (levelNumber < 1 || levelNumber > this.totalLevels || !this.scrollView) return;\n\n        const targetIndex = levelNumber - 1;\n        const percent = targetIndex / (this.totalLevels - 1);\n        \n        ScrollViewHelper.scrollToHorizontalPercent(this.scrollView, percent, 0.3);\n        \n      \n    }\n\n    /**\n     * 关卡点击事件处理\n     */\n    private onLevelClicked(levelNumber: number) {\n        // 允许选择任何关卡（包括未解锁的）\n        this.currentSelectedLevel = levelNumber;\n        this.updateAllLevelsDisplay();\n        this.isAutoScrolling = true;\n        this.scrollToLevel(levelNumber);\n        this.updateInfoDisplay();\n\n       \n    }\n\n    /**\n     * 更新信息显示\n     */\n    private updateInfoDisplay() {\n        if (this.infoLabel) {\n            const levelData = this.levelDataList[this.currentSelectedLevel - 1];\n            let statusText = \"\";\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    statusText = \"未解锁\";\n                    break;\n                case LevelStatus.CURRENT:\n                    statusText = \"进行中\";\n                    break;\n                case LevelStatus.COMPLETED:\n                    statusText = \"已通关\";\n                    break;\n            }\n            this.infoLabel.string = `当前选中: 关卡${this.currentSelectedLevel} (${statusText})`;\n        }\n    }\n\n    /**\n     * 完成当前关卡\n     */\n    private completeCurrentLevel() {\n        const levelData = this.levelDataList[this.currentSelectedLevel - 1];\n        if (levelData.status === LevelStatus.CURRENT) {\n            levelData.status = LevelStatus.COMPLETED;\n            \n            // 解锁下一关\n            if (this.currentSelectedLevel < this.totalLevels) {\n                const nextLevelData = this.levelDataList[this.currentSelectedLevel];\n                if (nextLevelData.status === LevelStatus.LOCKED) {\n                    nextLevelData.status = LevelStatus.CURRENT;\n                }\n            }\n            \n            this.updateAllLevelsDisplay();\n            this.updateInfoDisplay();\n        \n        }\n    }\n\n    /**\n     * 重置关卡\n     */\n    private resetLevels() {\n        for (let i = 0; i < this.levelDataList.length; i++) {\n            if (i === 0) {\n                this.levelDataList[i].status = LevelStatus.CURRENT;\n            } else {\n                this.levelDataList[i].status = LevelStatus.LOCKED;\n            }\n        }\n        this.currentSelectedLevel = 1;\n        this.updateAllLevelsDisplay();\n        this.scrollToLevel(1);\n        this.updateInfoDisplay();\n      \n    }\n\n    /**\n     * 设置随机进度\n     */\n    private setRandomProgress() {\n        const completedLevels = Math.floor(Math.random() * 10) + 1;\n        const currentLevel = Math.min(completedLevels + 1, this.totalLevels);\n\n        for (let i = 0; i < this.levelDataList.length; i++) {\n            if (i < completedLevels) {\n                this.levelDataList[i].status = LevelStatus.COMPLETED;\n            } else if (i === completedLevels) {\n                this.levelDataList[i].status = LevelStatus.CURRENT;\n            } else {\n                this.levelDataList[i].status = LevelStatus.LOCKED;\n            }\n        }\n\n        this.currentSelectedLevel = currentLevel;\n        this.updateAllLevelsDisplay();\n        this.scrollToLevel(currentLevel);\n        this.updateInfoDisplay();\n       \n    }\n\n    /**\n     * 调试ScrollView信息\n     */\n    private debugScrollView() {\n        if (this.scrollView) {\n            ScrollViewHelper.debugScrollView(this.scrollView, \"LevelSelectTest\");\n        } else {\n           \n        }\n    }\n\n    /**\n     * 调试参数信息\n     */\n    private debugParameters() {\n        \n\n        // 计算连接线总宽度\n        const totalLineWidth = (this.lineCount - 1) * this.lineSpacing;\n        \n\n        // 计算可用宽度\n        const availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);\n       \n\n        if (totalLineWidth > availableWidth) {\n            const adjustedSpacing = availableWidth / (this.lineCount - 1);\n           \n        } else {\n           \n        }\n\n        \n    }\n\n    /**\n     * 动态调整连接线间距（用于调试）\n     */\n    public setLineSpacing(spacing: number) {\n        this.lineSpacing = spacing;\n       \n        // 重新创建关卡项目以应用新参数\n        this.createLevelItems();\n        this.updateAllLevelsDisplay();\n    }\n\n    /**\n     * 动态调整关卡到连接线距离（用于调试）\n     */\n    public setLevelToLineDistance(distance: number) {\n        this.levelToLineDistance = distance;\n       \n        // 重新创建关卡项目以应用新参数\n        this.createLevelItems();\n        this.updateAllLevelsDisplay();\n    }\n\n    /**\n     * 动态调整连接线数量（用于调试）\n     */\n    public setLineCount(count: number) {\n        this.lineCount = count;\n       \n        // 重新创建关卡项目以应用新参数\n        this.createLevelItems();\n        this.updateAllLevelsDisplay();\n    }\n\n    /**\n     * 更新标签外边框\n     */\n    private updateLabelOutline(outline: cc.LabelOutline, status: LevelStatus) {\n        let outlineColor: cc.Color;\n        switch (status) {\n            case LevelStatus.LOCKED:\n                // 未解锁边框为 #7B7B7B\n                outlineColor = cc.color(123, 123, 123);\n                break;\n            case LevelStatus.CURRENT:\n                // 当前玩到的关卡边框 #CF5800\n                outlineColor = cc.color(207, 88, 0);\n                break;\n            case LevelStatus.COMPLETED:\n                // 已解锁边框为 #119C0F\n                outlineColor = cc.color(17, 156, 15);\n                break;\n            default:\n                outlineColor = cc.color(123, 123, 123);\n                break;\n        }\n\n        outline.color = outlineColor;\n        outline.width = 1;\n    }\n}\n"]}