// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

const {ccclass, property} = cc._decorator;

@ccclass
export default class GridController extends cc.Component {

    @property(cc.Sprite)
    backgroundSprite: cc.Sprite = null;  // 格子背景

    @property(cc.SpriteFrame)
    normalFrame: cc.SpriteFrame = null;  // 正常状态的背景图

    @property(cc.SpriteFrame)
    hoverFrame: cc.SpriteFrame = null;  // 悬停状态的背景图

    @property(cc.SpriteFrame)
    occupiedFrame: cc.SpriteFrame = null;  // 已占用状态的背景图

    // 格子坐标
    public gridX: number = 0;
    public gridY: number = 0;

    // 格子状态
    public isOccupied: boolean = false;
    public playerNode: cc.Node = null;

    // 回调函数
    public onGridClickCallback: (x: number, y: number) => void = null;

    onLoad() {
        // 添加触摸事件
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.on(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.on(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
    }

    start() {
        this.updateVisualState();
    }

    // 设置格子坐标
    public setGridCoordinate(x: number, y: number) {
        this.gridX = x;
        this.gridY = y;
        this.node.name = `Grid_${x}_${y}`;
    }

    // 设置点击回调
    public setClickCallback(callback: (x: number, y: number) => void) {
        this.onGridClickCallback = callback;
    }

    // 触摸开始
    private onTouchStart(event: cc.Event.EventTouch) {
        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {
            this.backgroundSprite.spriteFrame = this.hoverFrame;
        }
    }

    // 触摸结束
    private onTouchEnd(event: cc.Event.EventTouch) {
        this.updateVisualState();
        
        // 触发点击回调
        if (this.onGridClickCallback) {
            this.onGridClickCallback(this.gridX, this.gridY);
        }
    }

    // 触摸取消
    private onTouchCancel(event: cc.Event.EventTouch) {
        this.updateVisualState();
    }

    // 鼠标进入
    private onMouseEnter(event: cc.Event.EventMouse) {
        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {
            this.backgroundSprite.spriteFrame = this.hoverFrame;
        }
    }

    // 鼠标离开
    private onMouseLeave(event: cc.Event.EventMouse) {
        this.updateVisualState();
    }

    // 更新视觉状态
    private updateVisualState() {
        if (!this.backgroundSprite) return;

        if (this.isOccupied && this.occupiedFrame) {
            this.backgroundSprite.spriteFrame = this.occupiedFrame;
        } else if (this.normalFrame) {
            this.backgroundSprite.spriteFrame = this.normalFrame;
        }
    }

    // 设置占用状态
    public setOccupied(occupied: boolean, playerNode?: cc.Node) {
        this.isOccupied = occupied;
        this.playerNode = playerNode || null;
        this.updateVisualState();
    }

    // 获取格子世界坐标
    public getWorldPosition(): cc.Vec2 {
        return this.node.convertToWorldSpaceAR(cc.Vec2.ZERO);
    }

    // 清除玩家
    public clearPlayer() {
        if (this.playerNode) {
            this.playerNode.removeFromParent();
            this.playerNode = null;
        }
        this.setOccupied(false);
    }

    onDestroy() {
        // 清理事件监听
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.off(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.off(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
    }

    // update (dt) {}
}
