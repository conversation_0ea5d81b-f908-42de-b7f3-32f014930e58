
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/en.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc55fhCTRdMjZxuQuVowyEX', 'en');
// resources/i18n/en.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: 'You have been asked to leave the room',
    LeaveRoom: 'The room is dissolved',
    InsufficientBalance: 'The current balance is insufficient, please go to purchase',
    GameRouteNotFound: 'Game route not found',
    NetworkError: 'network error',
    RoomIsFull: 'Room is full',
    EnterRoomNumber: 'Enter room number',
    GetUserInfoFailed: 'get user info failed',
    RoomDoesNotExist: 'Room does not exist',
    FailedToDeductGoldCoins: 'Failed to deduct gold coins',
    ExitApplication: 'Are you sure to leave?',
    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',
    NotEnoughPlayers: 'Not enough players',
    TheGameIsFullOfPlayers: 'The game is full of players',
    kickout2: 'Whether to kick {0} out of the room?',
    upSeat: 'Join',
    downSeat: 'Leave',
    startGame: 'Start',
    readyGame: 'Ready',
    cancelGame: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm',
    kickout3: 'Kick Out',
    back: 'Back',
    leave: 'Leave',
    music: 'Music',
    sound: 'Sound',
    join: 'Join',
    create: 'Create',
    auto: 'Auto',
    Room: 'Room',
    room_number: 'Room Number',
    copy: 'Copy',
    game_amount: 'Game Amount',
    player_numbers: 'Player Numbers:',
    room_exist: 'Room doesn’t exist',
    enter_room_number: 'Enter room number',
    free: 'Free',
    players: 'Players',
    Player: 'Player',
    Tickets: 'Tickets',
    Empty: 'Empty',
    //帮助
    Tips: "Tips",
    Tips1: "1. Game Duration per Round: 120 seconds ",
    Tips2: "2. Ranking Rules: Upon the conclusion of the game time, players are ranked based on their scores. Players with the same score will share the same ranking. ",
    Tips3: "3. Elimination Rule: By swapping adjacent food items in the game, players can align identical food items. If 3 or more identical food items are adjacent after the swap, the elimination is successful, and points are awarded. ",
    Tips4: "4. Item Elimination: Completing special eliminations will generate elimination items. These items will be used and consumed when swapped with adjacent blocks. Details are as follows:",
    Tips5: "5.Combo Elimination: When a player's elimination action triggers the generation of new food items, and the subsequent falling of these items leads to further eliminations, this is considered a combo elimination. During combo eliminations, players cannot perform any actions. Each sequence of eliminations caused by a single round of falling food items is counted as 1 round.",
    //产生方式
    Generation_Method: "Generation Method & Effect",
    Generation_Method1: "1. Eliminate an entire row or column of food items.",
    Generation_Method2: "2. Eliminate 12 food items extending outward from its center.",
    Generation_Method3: "3. Eliminate all food items on the screen that are the same as the one swapped with the item. ",
    Generation_Method4: "4. Eliminate one row and one column of food items, including chains and ice blocks.  ",
    Generation_Method5: "5. Eliminate three rows and three columns of food items, including chains and ice blocks. ",
    Generation_Method6: "6. Transform a random type of food item on the screen into a rocket and activate it.  ",
    Generation_Method7: "7. Eliminate 24 food items extending outward from its center, including chains and ice blocks. ",
    Generation_Method8: "8. Transform a random food block on the screen into a bomb and activate it.  ",
    Generation_Method9: "9. Eliminate all food items on the screen, including chains and ice blocks.",
    //常驻任务
    Permanent_Task: "Permanent Task",
    Permanent_Task1: "Every time a player eliminates 6 crabs, they will receive a reward of randomly eliminating a 2x2 area twice. If the current crab elimination satisfies the condition twice or more, only one 2x2 elimination will be granted starting from the second time.",
    //随机任务
    Random_Task: "Random Task",
    Random_Task1: "The required elimination count scales with player count: +1 elimination per additional player (e.g. 2 players require 7 eliminations, 3 players require 8).",
    Random_Task2: "- Maximum 1 active mission at a time",
    Random_Task3: "- Mission sequence is randomly generated at match start and executed in sequence",
    Random_Task4: "- Eliminate 7xdesignated colors \u2192 Spawn 1 random special candy",
    Random_Task5: "- Eliminate 7xdesignated colors \u2192 Spawn 1 ice block",
    Random_Task6: "- Eliminate 7xdesignated colors \u2192 Spawn 1 chain lock",
    Random_Task7: "- Eliminate 7xdesignated colors \u2192 Spawn 1 random-shaped rocket item",
    //锁链简介
    Chains: "Chains",
    Chains1: "1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item\u2014**chains**\u2014to other players.  ",
    Chains2: "2. Hindrance Effect of Chains: Randomly locks a food item in a specific position. During the restriction period, the locked position cannot be moved. The food item within the chain may change due to eliminations in other areas, meaning it does not lock a fixed food item.  ",
    Chains3: "3. Chain Removal: Chains can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the chain.  ",
    //冰块简介
    Ice_Blocks: "Ice Blocks:",
    Ice_Blocks1: "1. Elimination Task: After the game starts, each player will receive a task assigned by the system. Completing the task will release a hindrance item\u2014**ice blocks**\u2014to other players.  ",
    Ice_Blocks2: "2. Hindrance Effect of Ice Blocks: Randomly freezes a food item. During the freezing period, the position can be passively moved, but players cannot actively move it.  ",
    Ice_Blocks3: "3. Ice Block Removal: Ice blocks can be removed by performing a normal elimination in an adjacent position (blocks up, down, left, or right within 1 grid) or within the effective area of an item. One elimination is sufficient to remove the ice block.",
    //得分细则
    Scoring_Details: "Scoring Details",
    Scoring_Details1: "Normal Elimination: Score per block eliminated : 20 points;",
    Scoring_Details2: "Consecutive Elimination: Score per block eliminated : Number of consecutive eliminations x 20 points;",
    Scoring_Details3: "Rocket Elimination: Rocket activation score : 300 points; Score per block eliminated by rocket : 30 points;",
    Scoring_Details4: "Bomb Elimination: Bomb activation score : 500 points; Score per block eliminated by bomb : 50 points;",
    Scoring_Details5: "Rainbow Elimination: Rainbow activation score : 600 points; Score per block eliminated by rainbow : 60 points;",
    Scoring_Details6: "Combined Rocket Elimination: Dual rocket activation score : 1200 points; Score per block eliminated by super rocket : 60 points;",
    Scoring_Details7: "Bomb-Rocket Combined Elimination: Bomb-rocket combination activation score : 1600 points; Score per block eliminated by bomb-rocket : 80 points;",
    Scoring_Details8: "Rainbow-Rocket Combined Elimination: Rainbow-rocket combination activation score : 1800 points; Score per block eliminated by rainbow-rocket : 90 points;",
    Scoring_Details9: "Combined Bomb Elimination: Super bomb activation score : 2000 points; Score per block eliminated by super bomb : 100 points;",
    Scoring_Details10: "Rainbow Bomb Elimination: Rainbow bomb activation score : 2200 points; Score per block eliminated by rainbow bomb : 110 points;",
    Scoring_Details11: "Combined Rainbow Elimination: Super rainbow activation score : 2400 points; Score per block eliminated by super rainbow : 120 points;",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.en = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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