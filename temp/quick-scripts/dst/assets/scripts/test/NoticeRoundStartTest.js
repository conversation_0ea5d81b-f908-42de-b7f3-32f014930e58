
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/NoticeRoundStartTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1b2cPU5fZ4kKvN7xI0VniQ', 'NoticeRoundStartTest');
// scripts/test/NoticeRoundStartTest.ts

"use strict";
// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeRoundStartTest = /** @class */ (function (_super) {
    __extends(NoticeRoundStartTest, _super);
    function NoticeRoundStartTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.statusLabel = null;
        return _this;
    }
    NoticeRoundStartTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试NoticeRoundStart消息';
        }
    };
    // 发送测试的NoticeRoundStart消息
    NoticeRoundStartTest.prototype.sendTestMessage = function () {
        console.log('发送测试NoticeRoundStart消息');
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001\u6D4B\u8BD5\u6D88\u606F: \u56DE\u5408" + testData.roundNumber + ", \u5012\u8BA1\u65F6" + testData.countDown + "\u79D2";
        }
    };
    // 发送倒计时更新测试
    NoticeRoundStartTest.prototype.sendCountdownUpdate = function (seconds) {
        var testData = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5012\u8BA1\u65F6\u66F4\u65B0: " + seconds + "\u79D2";
        }
    };
    // 测试不同的倒计时值
    NoticeRoundStartTest.prototype.testDifferentCountdowns = function () {
        var _this = this;
        // 测试25秒倒计时
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(25);
        }, 1);
        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(20);
        }, 3);
        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(5);
        }, 5);
        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(0);
        }, 7);
    };
    // 发送NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayMessage = function () {
        console.log('发送测试NoticeActionDisplay消息');
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1 // 挖掘
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2 // 标记
                }
            ],
            message: "展示阶段：显示所有玩家操作"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5C55\u793A\u9636\u6BB5\uFF0C\u5269\u4F59" + testData.countDown + "\u79D2";
        }
    };
    // 测试完整的回合流程
    NoticeRoundStartTest.prototype.testFullRoundFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 20秒后发送操作展示
        this.scheduleOnce(function () {
            _this.sendActionDisplayMessage();
        }, 2);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    };
    NoticeRoundStartTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Label)
    ], NoticeRoundStartTest.prototype, "statusLabel", void 0);
    NoticeRoundStartTest = __decorate([
        ccclass
    ], NoticeRoundStartTest);
    return NoticeRoundStartTest;
}(cc.Component));
exports.default = NoticeRoundStartTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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