
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/PlayerGameController .js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd06f40RGNtELrdV8XT/vrpM', 'PlayerGameController ');
// scripts/pfb/PlayerGameController .ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerGameController = /** @class */ (function (_super) {
    __extends(PlayerGameController, _super);
    function PlayerGameController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.flagNode = null; //旗子节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        return _this;
        // update (dt) {}
    }
    PlayerGameController.prototype.start = function () {
    };
    PlayerGameController.prototype.setData = function (user) {
        var _this = this;
        this.scheduleOnce(function () {
            if (user == null) {
                _this.avatar.active = false;
            }
            else {
                Tools_1.Tools.setNodeSpriteFrameUrl(_this.avatar, user.avatar); //添加头像
                _this.avatar.active = true;
            }
        }, 0.1);
    };
    /**
     * 显示加分效果，带动画
     * @param addValue 加分数值
     */
    PlayerGameController.prototype.showAddScore = function (addValue) {
        var _this = this;
        console.log("PlayerGameController.showAddScore: +" + addValue);
        if (this.addScoreNode) {
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                    console.log("\u8BBE\u7F6EPlayerGame change_score\u6587\u672C: " + labelComponent.string);
                }
            }
            else {
                console.warn("PlayerGame addScoreNode中找不到change_score子节点");
            }
            console.log("开始显示PlayerGame addScoreNode动画");
            // 停止之前的动画
            cc.Tween.stopAllByTarget(this.addScoreNode);
            // 重置节点状态
            this.addScoreNode.active = true;
            this.addScoreNode.opacity = 0;
            this.addScoreNode.scale = 0.8;
            // 保存原始位置
            var originalY_1 = this.addScoreNode.y;
            // 使用新的Tween API
            cc.tween(this.addScoreNode)
                .parallel(cc.tween().to(0.15, { opacity: 255 }), cc.tween().to(0.15, { scale: 1.1 }), cc.tween().by(0.15, { y: 15 }))
                .to(0.1, { scale: 1.0 })
                .delay(0.8)
                .parallel(cc.tween().to(0.25, { opacity: 0 }), cc.tween().to(0.25, { scale: 0.9 }), cc.tween().by(0.25, { y: 8 }))
                .call(function () {
                _this.addScoreNode.active = false;
                _this.addScoreNode.opacity = 255;
                _this.addScoreNode.scale = 1.0;
                _this.addScoreNode.y = originalY_1;
                console.log("PlayerGame addScoreNode动画完成");
            })
                .start();
        }
        else {
            console.warn("PlayerGame addScoreNode未设置");
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerGameController.prototype.showSubScore = function (subValue) {
        var _this = this;
        console.log("PlayerGameController.showSubScore: -" + subValue);
        if (this.subScoreNode) {
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                    console.log("\u8BBE\u7F6EPlayerGame change_score\u6587\u672C: " + labelComponent.string);
                }
            }
            else {
                console.warn("PlayerGame subScoreNode中找不到change_score子节点");
            }
            this.subScoreNode.active = true;
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                    console.log("1秒后隐藏PlayerGame subScoreNode");
                }
            }, 1.0);
        }
        else {
            console.warn("PlayerGame subScoreNode未设置");
        }
    };
    // 隐藏加减分节点
    PlayerGameController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "flagNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "subScoreNode", void 0);
    PlayerGameController = __decorate([
        ccclass
    ], PlayerGameController);
    return PlayerGameController;
}(cc.Component));
exports.default = PlayerGameController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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