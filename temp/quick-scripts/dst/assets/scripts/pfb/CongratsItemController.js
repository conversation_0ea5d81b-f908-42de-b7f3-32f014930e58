
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/CongratsItemController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4483cBqWUJAG7hafZbyIkLC', 'CongratsItemController');
// scripts/pfb/CongratsItemController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var Config_1 = require("../util/Config");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var ccclass = cc._decorator.ccclass;
var CongratsItemController = /** @class */ (function (_super) {
    __extends(CongratsItemController, _super);
    function CongratsItemController() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CongratsItemController.prototype.start = function () {
        this.boardIconCrownNode = this.node.getChildByName('board_icon_crown'); //皇冠的节点
        this.boardNum = this.node.getChildByName('board_num'); //皇冠的节点
        this.avatarNode = this.node.getChildByName('avatar'); //头像的节点
        this.nameNode = this.node.getChildByName('name_layout').getChildByName('name'); //名称的节点
        this.stepNode = this.node.getChildByName("step_layout").getChildByName('step'); // 步数的节点
        this.numberNode = this.node.getChildByName('congrats_list_frame').getChildByName('number_view').getChildByName('number'); //金豆的节点
        this.beanIcon = this.node.getChildByName('congrats_list_frame').getChildByName('board_icon_beans'); //金豆图标
    };
    //设置数据
    //settleType:结算类型
    //intUserID:中断游戏的玩家Id
    CongratsItemController.prototype.createData = function (settlement, gameUsers) {
        if (settlement.rank <= 3) {
            this.boardIconCrownNode.active = true;
            this.boardNum.active = false;
            //设置皇冠图片
            switch (settlement.rank) {
                case 1:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_01');
                    break;
                case 2:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_02');
                    break;
                case 3:
                    Tools_1.Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config_1.Config.gameRes + 'board_icon_crown_03');
                    break;
            }
        }
        else {
            this.boardIconCrownNode.active = false;
            this.boardNum.active = true;
            this.boardNum.getComponent(cc.Label).string = settlement.rank + ''; //显示名次
        }
        this.stepNode.getComponent(cc.Label).string = settlement.score + ''; //显示分数
        var index = gameUsers.findIndex(function (item) { return item.userId === settlement.userId; }); //搜索
        if (index != -1) {
            var user = gameUsers[index];
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatarNode, user.avatar);
            this.nameNode.getComponent(NickNameLabel_1.default).string = user.nickName;
        }
        this.numberNode.getComponent(cc.Label).string = Tools_1.Tools.NumToTBMK(settlement.coinChg);
        if (Publish_1.Publish.GetInstance().currencyIcon != null && Publish_1.Publish.GetInstance().currencyIcon !== '') {
            Tools_1.Tools.setNodeSpriteFrameUrl(this.beanIcon, Publish_1.Publish.GetInstance().currencyIcon);
        }
    };
    CongratsItemController = __decorate([
        ccclass
    ], CongratsItemController);
    return CongratsItemController;
}(cc.Component));
exports.default = CongratsItemController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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