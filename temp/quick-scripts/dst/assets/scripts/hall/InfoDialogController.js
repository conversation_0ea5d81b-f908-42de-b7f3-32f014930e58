
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var InfoItemController_1 = require("../pfb/InfoItemController");
var InfoItemOneController_1 = require("../pfb/InfoItemOneController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        this.infoImageList = [
            this.infoImage3,
            this.infoImage4,
            this.infoImage5,
            this.infoImage6,
            this.infoImage7,
            this.infoImage8,
            this.infoImage9,
            this.infoImage10,
            this.infoImage11,
        ];
        this.titleList = [
            window.getLocalizedStr('Tips'),
            window.getLocalizedStr('Generation_Method'),
            window.getLocalizedStr('Permanent_Task'),
            window.getLocalizedStr('Random_Task'),
            window.getLocalizedStr('Chains'),
            window.getLocalizedStr('Ice_Blocks'),
            window.getLocalizedStr('Scoring_Details'),
        ]; //title 的列表
        this.tipsList = [
            window.getLocalizedStr('Tips1'),
            window.getLocalizedStr('Tips2'),
            window.getLocalizedStr('Tips3'),
            window.getLocalizedStr('Tips4'),
            window.getLocalizedStr('Tips5'),
        ];
        this.generationMethodList = [
            window.getLocalizedStr('Generation_Method1'),
            window.getLocalizedStr('Generation_Method2'),
            window.getLocalizedStr('Generation_Method3'),
            window.getLocalizedStr('Generation_Method4'),
            window.getLocalizedStr('Generation_Method5'),
            window.getLocalizedStr('Generation_Method6'),
            window.getLocalizedStr('Generation_Method7'),
            window.getLocalizedStr('Generation_Method8'),
            window.getLocalizedStr('Generation_Method9'),
        ];
        this.permanentList = [
            window.getLocalizedStr('Permanent_Task1'),
        ];
        this.randomList = [
            window.getLocalizedStr('Random_Task1'),
            window.getLocalizedStr('Random_Task2'),
            window.getLocalizedStr('Random_Task3'),
            window.getLocalizedStr('Random_Task4'),
            window.getLocalizedStr('Random_Task5'),
            window.getLocalizedStr('Random_Task6'),
            window.getLocalizedStr('Random_Task7'),
        ];
        this.chainsList = [
            window.getLocalizedStr('Chains1'),
            window.getLocalizedStr('Chains2'),
            window.getLocalizedStr('Chains3'),
        ];
        this.iceList = [
            window.getLocalizedStr('Ice_Blocks1'),
            window.getLocalizedStr('Ice_Blocks2'),
            window.getLocalizedStr('Ice_Blocks3'),
        ];
        this.scoringDetails = [
            window.getLocalizedStr('Scoring_Details1'),
            window.getLocalizedStr('Scoring_Details2'),
            window.getLocalizedStr('Scoring_Details3'),
            window.getLocalizedStr('Scoring_Details4'),
            window.getLocalizedStr('Scoring_Details5'),
            window.getLocalizedStr('Scoring_Details6'),
            window.getLocalizedStr('Scoring_Details7'),
            window.getLocalizedStr('Scoring_Details8'),
            window.getLocalizedStr('Scoring_Details9'),
            window.getLocalizedStr('Scoring_Details10'),
            window.getLocalizedStr('Scoring_Details11'),
        ];
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        this.contentLay.removeAllChildren();
        this.getTitleNode(this.titleList[0]);
        this.tipsList.forEach(function (title, index) {
            var infoItem = cc.instantiate(_this.infoItem1); //初始化一个预制体
            var infoItemOneController = infoItem.getComponent(InfoItemOneController_1.default);
            infoItemOneController.setData(title);
            _this.contentLay.addChild(infoItem);
        });
        this.getTitleNode(this.titleList[1]);
        this.generationMethodList.forEach(function (title, index) {
            var infoItem = cc.instantiate(_this.infoItem1); //初始化一个预制体
            var infoItemOneController = infoItem.getComponent(InfoItemOneController_1.default);
            infoItemOneController.setData(title);
            var infoImg = cc.instantiate(_this.infoImageList[index]);
            infoItemOneController.setimgNode(infoImg);
            _this.contentLay.addChild(infoItem);
        });
        this.getTitleNode(this.titleList[2]);
        this.permanentList.forEach(function (title, index) {
            var infoItem = cc.instantiate(_this.infoItem1); //初始化一个预制体
            var infoItemOneController = infoItem.getComponent(InfoItemOneController_1.default);
            infoItemOneController.setData(title);
            _this.contentLay.addChild(infoItem);
        });
        var infoImg1 = cc.instantiate(this.infoImage1);
        this.contentLay.addChild(infoImg1);
        this.getTitleNode(this.titleList[3]);
        this.randomList.forEach(function (title, index) {
            var infoItem = cc.instantiate(_this.infoItem1); //初始化一个预制体
            var infoItemOneController = infoItem.getComponent(InfoItemOneController_1.default);
            infoItemOneController.setData(title);
            _this.contentLay.addChild(infoItem);
        });
        var infoImg2 = cc.instantiate(this.infoImage2);
        this.contentLay.addChild(infoImg2);
        this.getTitleNode(this.titleList[4]);
        this.chainsList.forEach(function (title, index) {
            var infoItem = cc.instantiate(_this.infoItem1); //初始化一个预制体
            var infoItemOneController = infoItem.getComponent(InfoItemOneController_1.default);
            infoItemOneController.setData(title);
            _this.contentLay.addChild(infoItem);
        });
        this.getTitleNode(this.titleList[5]);
        this.iceList.forEach(function (title, index) {
            var infoItem = cc.instantiate(_this.infoItem1); //初始化一个预制体
            var infoItemOneController = infoItem.getComponent(InfoItemOneController_1.default);
            infoItemOneController.setData(title);
            _this.contentLay.addChild(infoItem);
        });
        this.getTitleNode(this.titleList[6]);
        this.scoringDetails.forEach(function (title, index) {
            var infoItem = cc.instantiate(_this.infoItem1); //初始化一个预制体
            var infoItemOneController = infoItem.getComponent(InfoItemOneController_1.default);
            infoItemOneController.setData(title);
            _this.contentLay.addChild(infoItem);
        });
    };
    InfoDialogController.prototype.getTitleNode = function (title) {
        var infoItem = cc.instantiate(this.infoItem); //初始化一个预制体
        var infoItemController = infoItem.getComponent(InfoItemController_1.default);
        infoItemController.setContent(title);
        this.contentLay.addChild(infoItem);
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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