
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a8190B83eBMQKXGXtajqzw/', 'LevelSelectExample');
// scripts/hall/Level/LevelSelectExample.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择使用示例
 * 这个脚本展示了如何快速创建一个关卡选择界面
 */
var LevelSelectExample = /** @class */ (function (_super) {
    __extends(LevelSelectExample, _super);
    function LevelSelectExample() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // UI节点
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 25;
        _this.levelItemWidth = 150;
        // 关卡节点列表
        _this.levelNodes = [];
        return _this;
    }
    LevelSelectExample.prototype.onLoad = function () {
        this.createUI();
        this.initLevelData();
        this.createLevelSelectUI();
    };
    LevelSelectExample.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    /**
     * 创建UI结构
     */
    LevelSelectExample.prototype.createUI = function () {
        // 创建背景
        var bg = new cc.Node("Background");
        bg.addComponent(cc.Sprite);
        bg.setContentSize(750, 1334);
        bg.color = cc.Color.BLACK;
        bg.parent = this.node;
        // 创建标题
        var titleNode = new cc.Node("Title");
        var titleLabel = titleNode.addComponent(cc.Label);
        titleLabel.string = "选择关卡";
        titleLabel.fontSize = 36;
        titleLabel.node.color = cc.Color.WHITE;
        titleNode.setPosition(0, 500);
        titleNode.parent = this.node;
        // 创建ScrollView
        var scrollViewNode = new cc.Node("ScrollView");
        this.scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(650, 150);
        scrollViewNode.setPosition(0, 0);
        scrollViewNode.parent = this.node;
        // 创建Viewport
        var viewport = new cc.Node("Viewport");
        viewport.addComponent(cc.Mask);
        viewport.setContentSize(650, 150);
        viewport.parent = scrollViewNode;
        // 创建Content
        this.content = new cc.Node("Content");
        this.content.setContentSize(650, 150);
        this.content.parent = viewport;
        // 设置ScrollView属性
        this.scrollView.content = this.content;
        this.scrollView.horizontal = true;
        this.scrollView.vertical = false;
        this.scrollView.inertia = true;
        this.scrollView.elastic = true;
        // 修复Scrollbar问题
        this.scrollView.horizontalScrollBar = null;
        this.scrollView.verticalScrollBar = null;
        // 创建信息标签
        var infoNode = new cc.Node("InfoLabel");
        this.infoLabel = infoNode.addComponent(cc.Label);
        this.infoLabel.string = "当前选中: 关卡1";
        this.infoLabel.fontSize = 24;
        this.infoLabel.node.color = cc.Color.WHITE;
        infoNode.setPosition(0, -200);
        infoNode.parent = this.node;
        // 创建测试按钮
        this.createTestButtons();
    };
    /**
     * 创建测试按钮
     */
    LevelSelectExample.prototype.createTestButtons = function () {
        var _this = this;
        // 完成关卡按钮
        var completeBtn = this.createButton("完成当前关卡", cc.v2(-150, -300), function () {
            _this.completeCurrentLevel();
        });
        // 重置按钮
        var resetBtn = this.createButton("重置关卡", cc.v2(0, -300), function () {
            _this.resetLevels();
        });
        // 随机进度按钮
        var randomBtn = this.createButton("随机进度", cc.v2(150, -300), function () {
            _this.setRandomProgress();
        });
    };
    /**
     * 创建按钮
     */
    LevelSelectExample.prototype.createButton = function (text, position, callback) {
        var btnNode = new cc.Node("Button");
        var button = btnNode.addComponent(cc.Button);
        var sprite = btnNode.addComponent(cc.Sprite);
        // 设置按钮外观
        btnNode.setContentSize(120, 40);
        btnNode.color = cc.Color.BLUE;
        btnNode.setPosition(position);
        btnNode.parent = this.node;
        // 添加文字
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = text;
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = btnNode;
        // 设置点击事件
        btnNode.on('click', callback, this);
        return btnNode;
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectExample.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED;
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡选择UI
     */
    LevelSelectExample.prototype.createLevelSelectUI = function () {
        if (!this.content)
            return;
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                var lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectExample.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线节点
     */
    LevelSelectExample.prototype.createLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        node.setContentSize(6, 6);
        node.color = cc.Color.WHITE;
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectExample.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var size = cc.size(46, 46);
        var color = cc.Color.GRAY;
        // 根据状态确定颜色和大小
        if (isSelected) {
            size = cc.size(86, 86);
        }
        switch (levelData.status) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                color = cc.Color.GRAY;
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                color = cc.Color.YELLOW;
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                color = cc.Color.GREEN;
                break;
        }
        // 设置节点大小和颜色
        node.setContentSize(size);
        node.color = color;
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectExample.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectExample.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectExample.prototype.onLevelClicked = function (levelNumber) {
        var levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
            cc.log("\u5173\u5361 " + levelNumber + " \u672A\u89E3\u9501\uFF01");
            return;
        }
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
        cc.log("\u9009\u62E9\u5173\u5361: " + levelNumber);
    };
    /**
     * 更新信息显示
     */
    LevelSelectExample.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectExample.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    /**
     * 重置关卡
     */
    LevelSelectExample.prototype.resetLevels = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i === 0) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = 1;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(1);
        this.updateInfoDisplay();
    };
    /**
     * 设置随机进度
     */
    LevelSelectExample.prototype.setRandomProgress = function () {
        var completedLevels = Math.floor(Math.random() * 10) + 1;
        var currentLevel = Math.min(completedLevels + 1, this.totalLevels);
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i < completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else if (i === completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = currentLevel;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(currentLevel);
        this.updateInfoDisplay();
    };
    LevelSelectExample = __decorate([
        ccclass
    ], LevelSelectExample);
    return LevelSelectExample;
}(cc.Component));
exports.default = LevelSelectExample;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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