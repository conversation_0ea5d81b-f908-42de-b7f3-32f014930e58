
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'aba46KnCk5KrY4mF3h25sUm', 'LevelSelectTest');
// scripts/hall/Level/LevelSelectTest.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择测试脚本
 * 这个脚本专门用于测试关卡选择功能，解决ScrollView配置问题
 */
var LevelSelectTest = /** @class */ (function (_super) {
    __extends(LevelSelectTest, _super);
    function LevelSelectTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 25;
        _this.levelItemWidth = 150;
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // UI组件
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        return _this;
    }
    LevelSelectTest.prototype.onLoad = function () {
        this.createCompleteUI();
        this.initLevelData();
        this.createLevelItems();
        this.setupScrollEvents();
    };
    LevelSelectTest.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    LevelSelectTest.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 创建完整的UI结构
     */
    LevelSelectTest.prototype.createCompleteUI = function () {
        // 设置画布背景色
        this.node.color = cc.Color.BLACK;
        // 创建标题
        var titleNode = new cc.Node("Title");
        var titleLabel = titleNode.addComponent(cc.Label);
        titleLabel.string = "关卡选择测试";
        titleLabel.fontSize = 36;
        titleLabel.node.color = cc.Color.WHITE;
        titleNode.setPosition(0, 400);
        titleNode.parent = this.node;
        // 使用ScrollViewHelper创建ScrollView
        var scrollViewData = ScrollViewHelper_1.ScrollViewHelper.createHorizontalScrollView(this.node, cc.size(650, 150));
        this.scrollView = scrollViewData.scrollView;
        this.content = scrollViewData.content;
        // 设置ScrollView位置
        scrollViewData.scrollView.node.setPosition(0, 0);
        // 创建信息标签
        var infoNode = new cc.Node("InfoLabel");
        this.infoLabel = infoNode.addComponent(cc.Label);
        this.infoLabel.string = "当前选中: 关卡1 (进行中)";
        this.infoLabel.fontSize = 24;
        this.infoLabel.node.color = cc.Color.WHITE;
        infoNode.setPosition(0, -150);
        infoNode.parent = this.node;
        // 创建控制按钮
        this.createControlButtons();
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectTest.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectTest.prototype.onScrolling = function () {
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectTest.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectTest.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    /**
     * 创建控制按钮
     */
    LevelSelectTest.prototype.createControlButtons = function () {
        var _this = this;
        var buttonY = -250;
        var buttonSpacing = 150;
        // 完成关卡按钮
        this.createButton("完成关卡", cc.v2(-buttonSpacing, buttonY), function () {
            _this.completeCurrentLevel();
        });
        // 重置按钮
        this.createButton("重置", cc.v2(0, buttonY), function () {
            _this.resetLevels();
        });
        // 随机进度按钮
        this.createButton("随机进度", cc.v2(buttonSpacing, buttonY), function () {
            _this.setRandomProgress();
        });
        // 调试按钮
        this.createButton("调试信息", cc.v2(-75, buttonY - 60), function () {
            _this.debugScrollView();
        });
        // 参数调试按钮
        this.createButton("调试参数", cc.v2(75, buttonY - 60), function () {
            _this.debugParameters();
        });
    };
    /**
     * 创建按钮
     */
    LevelSelectTest.prototype.createButton = function (text, position, callback) {
        var btnNode = new cc.Node("Button_" + text);
        btnNode.setContentSize(120, 40);
        btnNode.setPosition(position);
        btnNode.parent = this.node;
        // 添加背景
        var sprite = btnNode.addComponent(cc.Sprite);
        btnNode.color = cc.Color.BLUE;
        // 添加文字
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = text;
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = btnNode;
        // 添加按钮组件
        var button = btnNode.addComponent(cc.Button);
        button.target = btnNode;
        // 设置点击事件
        btnNode.on('click', callback, this);
        return btnNode;
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectTest.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED;
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡项目
     */
    LevelSelectTest.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is null!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectTest.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectTest.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectTest.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        node.color = cc.Color.WHITE;
        // 尝试加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
            else {
            }
        });
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectTest.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 根据状态和是否选中确定图片路径和大小
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green";
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小，确保不被图片原始大小覆盖
                node.setContentSize(size);
            }
            else {
                // 如果图片加载失败，使用颜色作为备选方案
                var color = cc.Color.GRAY;
                switch (levelData.status) {
                    case LevelSelectController_1.LevelStatus.LOCKED:
                        color = cc.Color.GRAY;
                        break;
                    case LevelSelectController_1.LevelStatus.CURRENT:
                        color = cc.Color.YELLOW;
                        break;
                    case LevelSelectController_1.LevelStatus.COMPLETED:
                        color = cc.Color.GREEN;
                        break;
                }
                node.color = color;
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectTest.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectTest.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels || !this.scrollView)
            return;
        var targetIndex = levelNumber - 1;
        var percent = targetIndex / (this.totalLevels - 1);
        ScrollViewHelper_1.ScrollViewHelper.scrollToHorizontalPercent(this.scrollView, percent, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectTest.prototype.onLevelClicked = function (levelNumber) {
        // 允许选择任何关卡（包括未解锁的）
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
    };
    /**
     * 更新信息显示
     */
    LevelSelectTest.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectTest.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    /**
     * 重置关卡
     */
    LevelSelectTest.prototype.resetLevels = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i === 0) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = 1;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(1);
        this.updateInfoDisplay();
    };
    /**
     * 设置随机进度
     */
    LevelSelectTest.prototype.setRandomProgress = function () {
        var completedLevels = Math.floor(Math.random() * 10) + 1;
        var currentLevel = Math.min(completedLevels + 1, this.totalLevels);
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i < completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else if (i === completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = currentLevel;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(currentLevel);
        this.updateInfoDisplay();
    };
    /**
     * 调试ScrollView信息
     */
    LevelSelectTest.prototype.debugScrollView = function () {
        if (this.scrollView) {
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectTest");
        }
        else {
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectTest.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 动态调整连接线间距（用于调试）
     */
    LevelSelectTest.prototype.setLineSpacing = function (spacing) {
        this.lineSpacing = spacing;
        // 重新创建关卡项目以应用新参数
        this.createLevelItems();
        this.updateAllLevelsDisplay();
    };
    /**
     * 动态调整关卡到连接线距离（用于调试）
     */
    LevelSelectTest.prototype.setLevelToLineDistance = function (distance) {
        this.levelToLineDistance = distance;
        // 重新创建关卡项目以应用新参数
        this.createLevelItems();
        this.updateAllLevelsDisplay();
    };
    /**
     * 动态调整连接线数量（用于调试）
     */
    LevelSelectTest.prototype.setLineCount = function (count) {
        this.lineCount = count;
        // 重新创建关卡项目以应用新参数
        this.createLevelItems();
        this.updateAllLevelsDisplay();
    };
    /**
     * 更新标签外边框
     */
    LevelSelectTest.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    LevelSelectTest = __decorate([
        ccclass
    ], LevelSelectTest);
    return LevelSelectTest;
}(cc.Component));
exports.default = LevelSelectTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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