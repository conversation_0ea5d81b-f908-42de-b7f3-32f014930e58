
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '40c9e3ykUFClLHG7s1cQEKm', 'LevelSelectController');
// scripts/hall/Level/LevelSelectController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelStatus = void 0;
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 关卡状态枚举
var LevelStatus;
(function (LevelStatus) {
    LevelStatus[LevelStatus["LOCKED"] = 0] = "LOCKED";
    LevelStatus[LevelStatus["CURRENT"] = 1] = "CURRENT";
    LevelStatus[LevelStatus["COMPLETED"] = 2] = "COMPLETED"; // 已通关（绿色）
})(LevelStatus = exports.LevelStatus || (exports.LevelStatus = {}));
var LevelSelectController = /** @class */ (function (_super) {
    __extends(LevelSelectController, _super);
    function LevelSelectController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 25;
        _this.screenWidth = 650;
        _this.levelItemWidth = 150; // 关卡项的宽度（包括间距）
        _this.visibleLevels = 3; // 可见关卡数量
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // 关卡节点列表
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        // 关卡选择变化回调
        _this.onLevelSelectionChanged = null;
        return _this;
    }
    LevelSelectController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        this.initLevelData();
        this.createLevelItems();
        this.updateLevelDisplay();
        this.setupScrollEvents();
    };
    LevelSelectController.prototype.start = function () {
        // 初始化滚动到第一关
        this.scrollToLevel(this.currentSelectedLevel);
    };
    LevelSelectController.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectController.prototype.initLevelData = function () {
        this.levelDataList = [];
        // 测试数据：可以修改这个数值来测试不同的关卡进度
        // 例如：0 = 只有第1关可玩，3 = 前3关已完成第4关可玩，10 = 前10关已完成第11关可玩
        var completedLevels = 5; // 已完成的关卡数
        var currentLevel = Math.min(completedLevels + 1, this.totalLevels); // 当前关卡是已完成关卡的下一关
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i <= completedLevels) {
                status = LevelStatus.COMPLETED; // 已完成的关卡
            }
            else if (i === currentLevel) {
                status = LevelStatus.CURRENT; // 当前关卡（最新解锁的关卡）
            }
            else {
                status = LevelStatus.LOCKED; // 未解锁的关卡
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
        // 设置当前选中关卡为当前关卡
        this.currentSelectedLevel = currentLevel;
    };
    /**
     * 创建关卡项目
     */
    LevelSelectController.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectController.prototype.createLevelNode = function (levelData) {
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        var eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = "LevelSelectController";
        eventHandler.handler = "onLevelClicked";
        eventHandler.customEventData = levelData.levelNumber.toString();
        button.clickEvents.push(eventHandler);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectController.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectController.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
        });
        return node;
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelSelectController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectController.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后强制设置正确的大小，覆盖图片原始大小
                node.setContentSize(size);
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新关卡显示
     */
    LevelSelectController.prototype.updateLevelDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectController.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectController.prototype.onLevelClicked = function (event, customEventData) {
        var levelNumber = parseInt(customEventData);
        // 允许选择任何关卡（包括未解锁的）
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateLevelDisplay();
        // 滚动到选中关卡
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(levelNumber);
        }
        // 这里可以添加进入关卡的逻辑
        // this.enterLevel(levelNumber);
    };
    /**
     * 设置关卡状态
     */
    LevelSelectController.prototype.setLevelStatus = function (levelNumber, status) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        this.levelDataList[levelNumber - 1].status = status;
        this.updateLevelDisplay();
    };
    /**
     * 获取当前选中的关卡
     */
    LevelSelectController.prototype.getCurrentSelectedLevel = function () {
        return this.currentSelectedLevel;
    };
    /**
     * 获取指定关卡的数据
     */
    LevelSelectController.prototype.getLevelData = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return null;
        return this.levelDataList[levelNumber - 1];
    };
    /**
     * 设置关卡进度（从外部调用，比如从后端获取数据后）
     * @param completedLevels 已完成的关卡数
     */
    LevelSelectController.prototype.setLevelProgress = function (completedLevels) {
        if (completedLevels < 0 || completedLevels > this.totalLevels)
            return;
        var currentLevel = Math.min(completedLevels + 1, this.totalLevels);
        // 重新设置所有关卡状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i <= completedLevels) {
                status = LevelStatus.COMPLETED; // 已完成的关卡
            }
            else if (i === currentLevel && completedLevels < this.totalLevels) {
                status = LevelStatus.CURRENT; // 当前关卡（最新解锁的关卡）
            }
            else {
                status = LevelStatus.LOCKED; // 未解锁的关卡
            }
            this.levelDataList[i - 1].status = status;
        }
        // 更新当前选中关卡
        this.currentSelectedLevel = currentLevel;
        this.updateLevelDisplay();
        this.scrollToLevel(currentLevel);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(currentLevel);
        }
    };
    /**
     * 解锁下一关
     */
    LevelSelectController.prototype.unlockNextLevel = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (this.levelDataList[i].status === LevelStatus.LOCKED) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
                this.updateLevelDisplay();
                break;
            }
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectController.prototype.completeCurrentLevel = function () {
        var currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (currentLevelData.status === LevelStatus.CURRENT) {
            currentLevelData.status = LevelStatus.COMPLETED;
            this.unlockNextLevel();
            this.updateLevelDisplay();
        }
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectController.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectController.prototype.onScrolling = function () {
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectController.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectController.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateLevelDisplay();
            // 通知关卡选择变化
            if (this.onLevelSelectionChanged) {
                this.onLevelSelectionChanged(closestLevel);
            }
        }
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectController.prototype.fixScrollViewScrollbar = function () {
        if (this.scrollView && this.content) {
            ScrollViewHelper_1.ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectController");
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectController.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 更新标签外边框
     */
    LevelSelectController.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    /**
     * 重新创建关卡项目（用于参数调整后刷新）
     */
    LevelSelectController.prototype.refreshLevelItems = function () {
        this.createLevelItems();
        this.updateLevelDisplay();
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 测试方法：快速设置不同的关卡进度场景
     */
    LevelSelectController.prototype.setTestScenario = function (scenario) {
        var completedLevels;
        switch (scenario) {
            case 1:
                completedLevels = 0; // 只有第1关可玩
                break;
            case 2:
                completedLevels = 3; // 前3关已完成，第4关可玩
                break;
            case 3:
                completedLevels = 10; // 前10关已完成，第11关可玩
                break;
            case 4:
                completedLevels = 20; // 前20关已完成，第21关可玩
                break;
            case 5:
                completedLevels = 25; // 全部关卡已完成
                break;
            default:
                completedLevels = 5; // 默认：前5关已完成，第6关可玩
                break;
        }
        this.setLevelProgress(completedLevels);
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectController.prototype, "content", void 0);
    LevelSelectController = __decorate([
        ccclass
    ], LevelSelectController);
    return LevelSelectController;
}(cc.Component));
exports.default = LevelSelectController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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