
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eed91BOnAtLKYkInYtuL76o', 'LevelSelectPageController');
// scripts/hall/Level/LevelSelectPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelSelectPageController = /** @class */ (function (_super) {
    __extends(LevelSelectPageController, _super);
    function LevelSelectPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSelectController = null;
        _this.scrollView = null;
        _this.currentLevelLabel = null;
        _this.completeButton = null;
        _this.unlockButton = null;
        _this.resetButton = null;
        // 新增的游戏按钮
        _this.startGameButton = null;
        _this.lockedButton = null;
        return _this;
    }
    LevelSelectPageController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        // 设置按钮事件
        if (this.completeButton) {
            this.completeButton.node.on('click', this.onCompleteButtonClick, this);
        }
        if (this.unlockButton) {
            this.unlockButton.node.on('click', this.onUnlockButtonClick, this);
        }
        if (this.resetButton) {
            this.resetButton.node.on('click', this.onResetButtonClick, this);
        }
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    };
    LevelSelectPageController.prototype.start = function () {
        var _this = this;
        this.updateCurrentLevelDisplay();
        this.updateGameButtons();
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = function (levelNumber) {
                _this.onLevelSelectionChanged(levelNumber);
            };
        }
    };
    /**
     * 更新当前关卡显示
     */
    LevelSelectPageController.prototype.updateCurrentLevelDisplay = function () {
        if (this.currentLevelLabel && this.levelSelectController) {
            var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
            this.currentLevelLabel.string = "\u5F53\u524D\u9009\u4E2D\u5173\u5361: " + currentLevel;
        }
        // 同时更新游戏按钮状态
        this.updateGameButtons();
    };
    /**
     * 完成当前关卡按钮点击
     */
    LevelSelectPageController.prototype.onCompleteButtonClick = function () {
        if (this.levelSelectController) {
            this.levelSelectController.completeCurrentLevel();
            this.updateCurrentLevelDisplay();
            cc.log("完成当前关卡");
        }
    };
    /**
     * 解锁下一关按钮点击
     */
    LevelSelectPageController.prototype.onUnlockButtonClick = function () {
        if (this.levelSelectController) {
            this.levelSelectController.unlockNextLevel();
            this.updateCurrentLevelDisplay();
            cc.log("解锁下一关");
        }
    };
    /**
     * 重置关卡按钮点击
     */
    LevelSelectPageController.prototype.onResetButtonClick = function () {
        if (this.levelSelectController) {
            // 重置到初始状态：没有完成任何关卡，第1关为当前关卡
            this.levelSelectController.setLevelProgress(0);
            cc.log("重置所有关卡");
        }
    };
    /**
     * 进入选中的关卡
     */
    LevelSelectPageController.prototype.enterSelectedLevel = function () {
        if (this.levelSelectController) {
            var selectedLevel = this.levelSelectController.getCurrentSelectedLevel();
            cc.log("\u8FDB\u5165\u5173\u5361 " + selectedLevel);
            // 这里可以添加进入关卡的具体逻辑
            // 例如：切换到游戏场景，传递关卡参数等
        }
    };
    /**
     * 设置关卡进度（从外部调用）
     * @param completedLevels 已完成的关卡数，当前关卡会自动设置为 completedLevels + 1
     */
    LevelSelectPageController.prototype.setLevelProgress = function (completedLevels) {
        if (!this.levelSelectController)
            return;
        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(completedLevels);
        // 更新显示（LevelSelectController.setLevelProgress 会触发回调，但为了保险起见还是手动更新一次）
        this.updateCurrentLevelDisplay();
    };
    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     * @param currentLevel 当前关卡（会被忽略，自动计算为 completedLevels + 1）
     */
    LevelSelectPageController.prototype.setLevelProgressLegacy = function (completedLevels, currentLevel) {
        // 忽略 currentLevel 参数，使用新的逻辑
        this.setLevelProgress(completedLevels);
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectPageController.prototype.fixScrollViewScrollbar = function () {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }
        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    };
    /**
     * 更新游戏按钮显示状态
     */
    LevelSelectPageController.prototype.updateGameButtons = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (!levelData)
            return;
        // 根据关卡状态显示不同的按钮
        var isLocked = levelData.status === LevelSelectController_1.LevelStatus.LOCKED;
        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelSelectPageController.prototype.onStartGameButtonClick = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (levelData && levelData.status !== LevelSelectController_1.LevelStatus.LOCKED) {
            cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + currentLevel);
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    };
    /**
     * 未解锁按钮点击事件
     */
    LevelSelectPageController.prototype.onLockedButtonClick = function () {
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        cc.log("\u5173\u5361 " + currentLevel + " \u5C1A\u672A\u89E3\u9501");
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    };
    /**
     * 关卡选择变化回调
     */
    LevelSelectPageController.prototype.onLevelSelectionChanged = function (levelNumber) {
        this.updateCurrentLevelDisplay();
    };
    __decorate([
        property(LevelSelectController_1.default)
    ], LevelSelectPageController.prototype, "levelSelectController", void 0);
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectPageController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Label)
    ], LevelSelectPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "completeButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "unlockButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "resetButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "lockedButton", void 0);
    LevelSelectPageController = __decorate([
        ccclass
    ], LevelSelectPageController);
    return LevelSelectPageController;
}(cc.Component));
exports.default = LevelSelectPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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