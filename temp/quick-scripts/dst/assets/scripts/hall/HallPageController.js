
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '63546mbf2xAv6Hj/p5rHgOb', 'HallPageController');
// scripts/hall/HallPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HallOrMatch = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var WebSocketTool_1 = require("../net/WebSocketTool");
var ToastController_1 = require("../ToastController");
var AudioManager_1 = require("../util/AudioManager");
var HallParentController_1 = require("./HallParentController");
var InfoDialogController_1 = require("./InfoDialogController");
var KickOutDialogController_1 = require("./KickOutDialogController");
var LeaveDialogController_1 = require("./LeaveDialogController");
var MatchParentController_1 = require("./MatchParentController");
var SettingDialogController_1 = require("./SettingDialogController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallOrMatch;
(function (HallOrMatch) {
    HallOrMatch[HallOrMatch["HALL_PARENT"] = 0] = "HALL_PARENT";
    HallOrMatch[HallOrMatch["MATCH_PARENT"] = 1] = "MATCH_PARENT";
})(HallOrMatch = exports.HallOrMatch || (exports.HallOrMatch = {}));
var HallPageController = /** @class */ (function (_super) {
    __extends(HallPageController, _super);
    function HallPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.hallParentNode = null;
        _this.matchParentNode = null;
        _this.infoDialogController = null; //道具简介弹窗
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.settingDialogController = null; //设置弹窗
        _this.kickOutDialogController = null; //踢出用户的 dialog
        _this.toastController = null; //toast 的布局
        _this.hallOrMatch = null;
        return _this;
        // update (dt) {}
    }
    HallPageController.prototype.onLoad = function () {
        this.hallParentController = this.hallParentNode.getComponent(HallParentController_1.default);
        this.matchParentController = this.matchParentNode.getComponent(MatchParentController_1.default);
    };
    HallPageController.prototype.onEnable = function () {
        AudioManager_1.AudioManager.playBgm();
        this.setHallOrMatch(HallOrMatch.HALL_PARENT);
    };
    HallPageController.prototype.start = function () {
        var _this = this;
        this.hallParentController.setClick(function () {
            //返回键的回调
            _this.leaveDialogController.show(0, function () { });
        }, function () {
            //info 的回调
            _this.infoDialogController.show(function () { });
        }, function () {
            //设置键的回调
            _this.settingDialogController.show(function () { });
        }, function () {
            //start 按钮点击
            _this.startOrCreate(MessageId_1.MessageId.MsgTypePairRequest);
        }, function () {
            //create 点击创建房间
            _this.startOrCreate(MessageId_1.MessageId.MsgTypeCreateInvite);
        }, function (userId, nickname) {
            //点击创建房间内的 点击玩家头像 弹出的踢出房间弹窗
            _this.kickOutDialogController.show(userId, nickname);
        });
        this.matchParentController.setClick(function () {
            //匹配页面的返回键的回调
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelPair, {});
        });
    };
    HallPageController.prototype.updateGold = function () {
        this.hallParentController.updateGold();
    };
    //开始匹配 或者创建房间
    HallPageController.prototype.startOrCreate = function (msgId) {
        //判断是否链接成功，并且还得有登录成功的数据返回 ，不成功的话就不允许执行下面的操作
        if (WebSocketManager_1.WebSocketManager.GetInstance().webState != WebSocketTool_1.WebSocketToolState.Connected || GlobalBean_1.GlobalBean.GetInstance().loginData == null) {
            return;
        }
        //点击 快速开始游戏 start 的回调
        var pairRequest = {
            playerNum: GlobalBean_1.GlobalBean.GetInstance().players,
            fee: GlobalBean_1.GlobalBean.GetInstance().ticketsNum,
        };
        //发送请求开始游戏的消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(msgId, pairRequest);
    };
    //设置是大厅 还是匹配页面
    HallPageController.prototype.setHallOrMatch = function (hallOrMatch) {
        if (this.hallOrMatch === hallOrMatch) {
            return;
        }
        this.hallOrMatch = hallOrMatch;
        this.hallParentNode.active = false;
        this.matchParentNode.active = false;
        switch (hallOrMatch) {
            case HallOrMatch.HALL_PARENT:
                this.hallParentNode.active = true;
                break;
            case HallOrMatch.MATCH_PARENT:
                this.matchParentNode.active = true;
                break;
        }
    };
    HallPageController.prototype.LoginSuccess = function () {
        //登录成功后 执行的操作
        GameMgr_1.GameMgr.Console.Log("登录成功");
        var loginData = GlobalBean_1.GlobalBean.GetInstance().loginData;
        if (loginData) {
            if (loginData.roomId > 0) { //正在游戏中
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
            }
            if (loginData.inviteCode > 0) { //正在私人房间
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(loginData.inviteCode) });
            }
            else {
                //房间已经解散了  但是我还留在私人房间
                this.hallParentController.exitTheRoom();
            }
            //重连的时候 被遗留在匹配页面的话 就回到大厅
            if (loginData.roomId == 0 && loginData.inviteCode == 0 && this.hallOrMatch == HallOrMatch.MATCH_PARENT) {
                this.setHallOrMatch(HallOrMatch.HALL_PARENT);
            }
        }
        this.setFees();
    };
    //设置接受邀请成功
    HallPageController.prototype.setAcceptInvite = function (acceptInvite) {
        this.hallParentController.setAcceptInvite(acceptInvite);
    };
    //离开房间
    HallPageController.prototype.leaveRoom = function (noticeLeaveInvite) {
        this.hallParentController.leaveRoom(noticeLeaveInvite);
    };
    //设置门票
    HallPageController.prototype.setFees = function () {
        this.hallParentController.setFees();
    };
    //初始化 match 页面
    HallPageController.prototype.createMatchView = function () {
        this.matchParentController.createMatchView();
    };
    //设置匹配数据
    HallPageController.prototype.setGameData = function () {
        this.matchParentController.setGameData();
    };
    //进入私人房间
    HallPageController.prototype.joinCreateRoom = function () {
        this.hallParentController.joinCreateRoom();
    };
    //房间号无效
    HallPageController.prototype.joinError = function () {
        this.hallParentController.joinError();
    };
    //准备 取消准备
    HallPageController.prototype.setReadyState = function (noticeUserInviteStatus) {
        if (this.hallParentController) {
            this.hallParentController.setReadyState(noticeUserInviteStatus);
        }
    };
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "hallParentNode", void 0);
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "matchParentNode", void 0);
    __decorate([
        property(InfoDialogController_1.default)
    ], HallPageController.prototype, "infoDialogController", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], HallPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(SettingDialogController_1.default)
    ], HallPageController.prototype, "settingDialogController", void 0);
    __decorate([
        property(KickOutDialogController_1.default)
    ], HallPageController.prototype, "kickOutDialogController", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallPageController.prototype, "toastController", void 0);
    HallPageController = __decorate([
        ccclass
    ], HallPageController);
    return HallPageController;
}(cc.Component));
exports.default = HallPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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