
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GameScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ef3738C6EtNP63rWdv6bSAj', 'GameScoreController');
// scripts/game/GameScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var PlayerScoreController_1 = require("../pfb/PlayerScoreController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GameScoreController = /** @class */ (function (_super) {
    __extends(GameScoreController, _super);
    function GameScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scoreLayout = null; // 分数布局容器
        _this.playerScorePfb = null; // player_score_pfb 预制体
        _this._scoreControllers = []; // 分数控制器数组
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    GameScoreController.prototype.onLoad = function () {
        // 初始化时不自动创建界面，等待游戏数据
    };
    GameScoreController.prototype.start = function () {
        // 不在start中自动创建，等待外部调用
    };
    /**
     * 创建分数显示界面
     * 只使用后端传回来的真实游戏数据
     */
    GameScoreController.prototype.createScoreView = function () {
        console.log('开始创建分数显示界面');
        // 检查必要的组件是否存在
        if (!this.scoreLayout) {
            console.error("scoreLayout 未设置！请在编辑器中拖拽布局节点到 scoreLayout 属性");
            return;
        }
        if (!this.playerScorePfb) {
            console.error("playerScorePfb 未设置！请在编辑器中拖拽预制体到 playerScorePfb 属性");
            return;
        }
        // 只使用后端传回来的真实游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法创建分数界面。请等待 NoticeStartGame 消息");
            return;
        }
        // 获取后端传回来的用户数据
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        console.log("\u83B7\u53D6\u5230 " + users.length + " \u4E2A\u73A9\u5BB6\u6570\u636E");
        // 确保所有用户都有score字段，初始化为0
        users.forEach(function (user, index) {
            if (user.score === undefined || user.score === null) {
                user.score = 0;
            }
            console.log("\u73A9\u5BB6" + index + ": " + user.nickName + " (" + user.userId + "), \u5206\u6570: " + user.score);
        });
        // 清空现有的分数显示
        this.scoreLayout.removeAllChildren();
        this._scoreControllers = [];
        // 根据后端用户数据生成分数预制体
        for (var i = 0; i < users.length; i++) {
            var item = cc.instantiate(this.playerScorePfb);
            this.scoreLayout.addChild(item);
            var scoreController = item.getComponent(PlayerScoreController_1.default);
            if (scoreController) {
                this._scoreControllers.push(scoreController);
                scoreController.setData(users[i]);
                console.log("\u521B\u5EFA\u5206\u6570\u63A7\u5236\u5668 " + i + ": " + users[i].nickName);
            }
            else {
                console.error("预制体上没有找到 PlayerScoreController 组件");
            }
        }
        console.log("\u5206\u6570\u754C\u9762\u521B\u5EFA\u5B8C\u6210\uFF0C\u5171\u521B\u5EFA " + this._scoreControllers.length + " \u4E2A\u5206\u6570\u63A7\u5236\u5668");
    };
    /**
     * 初始化分数界面
     * 当收到 NoticeStartGame 消息后调用此方法
     */
    GameScoreController.prototype.initializeScoreView = function () {
        console.log('初始化分数界面');
        this.createScoreView();
    };
    /**
     * 设置游戏数据
     * 更新所有玩家的分数显示
     */
    GameScoreController.prototype.setGameData = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法设置分数数据");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        console.log("\u8BBE\u7F6E\u6E38\u620F\u6570\u636E\uFF0C\u66F4\u65B0 " + users.length + " \u4E2A\u73A9\u5BB6\u7684\u5206\u6570\u663E\u793A");
        // 更新所有玩家的分数显示
        for (var i = 0; i < users.length; i++) {
            if (i < this._scoreControllers.length) {
                this._scoreControllers[i].setData(users[i]);
                console.log("\u66F4\u65B0\u73A9\u5BB6" + i + "\u6570\u636E: " + users[i].nickName + ", \u5206\u6570: " + users[i].score);
            }
        }
    };
    /**
     * 更新特定玩家的分数
     * @param userId 玩家ID
     * @param score 新的分数
     */
    GameScoreController.prototype.updatePlayerScore = function (userId, score) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            this._scoreControllers[userIndex].updateScore(score);
            console.log("\u66F4\u65B0\u73A9\u5BB6\u5206\u6570: " + userId + ", \u65B0\u5206\u6570: " + score);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6\u6216\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex);
        }
    };
    /**
     * 更新所有玩家分数
     */
    GameScoreController.prototype.updateAllScores = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新所有玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        console.log("\u66F4\u65B0\u6240\u6709\u73A9\u5BB6\u5206\u6570\uFF0C\u5171 " + users.length + " \u4E2A\u73A9\u5BB6");
        for (var i = 0; i < users.length && i < this._scoreControllers.length; i++) {
            this._scoreControllers[i].updateScore(users[i].score || 0);
            console.log("\u66F4\u65B0\u73A9\u5BB6" + i + "\u5206\u6570: " + users[i].nickName + ", \u5206\u6570: " + (users[i].score || 0));
        }
    };
    /**
     * 获取指定索引的PlayerScoreController
     * @param userIndex 用户索引
     * @returns PlayerScoreController 或 null
     */
    GameScoreController.prototype.getPlayerScoreController = function (userIndex) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            return this._scoreControllers[userIndex];
        }
        return null;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GameScoreController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        console.log('收到首选玩家奖励通知:', data);
        console.log("\u56DE\u5408: " + data.roundNumber + ", \u73A9\u5BB6: " + data.userId + ", \u5956\u52B1\u5206\u6570: +" + data.bonusScore + ", \u603B\u5206: " + data.totalScore);
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理首选玩家奖励");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === data.userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            console.log("\u627E\u5230\u7528\u6237\u7D22\u5F15: " + userIndex + ", \u7528\u6237ID: " + data.userId);
            // 1. 更新玩家的总分数到全局数据
            users[userIndex].score = data.totalScore;
            console.log("\u66F4\u65B0\u5168\u5C40\u7528\u6237\u5206\u6570: " + data.totalScore);
            // 2. 更新分数显示 - 显示新的总分
            this._scoreControllers[userIndex].updateScore(data.totalScore);
            console.log("\u66F4\u65B0\u5206\u6570\u663E\u793A\u5B8C\u6210: " + data.totalScore);
            // 3. 显示加分效果动画 - 显示奖励分数
            this.showAddScoreWithAnimation(userIndex, data.bonusScore);
            console.log("\u663E\u793A\u52A0\u5206\u6548\u679C: +" + data.bonusScore);
            // 4. 判断是否为当前用户，如果是则同时更新player_game_pfb
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isMyself = (data.userId === currentUserId);
            if (isMyself) {
                // 更新对应的player_game_pfb中的change_score
                this.updatePlayerGameScore(data.userId, data.bonusScore);
                console.log("\u81EA\u5DF1\u83B7\u5F97\u9996\u9009\u5956\u52B1 +" + data.bonusScore + "\uFF0C\u603B\u5206: " + data.totalScore);
            }
            else {
                console.log("\u5176\u4ED6\u73A9\u5BB6\u83B7\u5F97\u9996\u9009\u5956\u52B1: " + data.userId + ", +" + data.bonusScore + "\uFF0C\u603B\u5206: " + data.totalScore);
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + data.userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
            // 打印所有用户信息用于调试
            console.log('所有用户信息:');
            users.forEach(function (user, index) {
                console.log("  [" + index + "] userId: " + user.userId + ", nickName: " + user.nickName);
            });
        }
    };
    /**
     * 显示加分效果动画
     * @param userIndex 用户索引
     * @param bonusScore 奖励分数
     */
    GameScoreController.prototype.showAddScoreWithAnimation = function (userIndex, bonusScore) {
        console.log("\u663E\u793A\u52A0\u5206\u52A8\u753B: userIndex=" + userIndex + ", bonusScore=+" + bonusScore);
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            var scoreController = this._scoreControllers[userIndex];
            // 调用PlayerScoreController的showAddScore方法显示加分效果
            // 这会在player_score_pfb的addscore/change_score中显示"+1"等文本
            scoreController.showAddScore(bonusScore);
            console.log("\u52A0\u5206\u52A8\u753B\u5DF2\u89E6\u53D1: +" + bonusScore);
        }
        else {
            console.warn("\u65E0\u6548\u7684\u7528\u6237\u7D22\u5F15: " + userIndex + ", \u63A7\u5236\u5668\u6570\u91CF: " + this._scoreControllers.length);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GameScoreController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        console.log("\u5C1D\u8BD5\u66F4\u65B0player_game_pfb\u4E2D\u7684change_score: userId=" + userId + ", bonus=+" + bonusScore);
        // 通过GamePageController来调用ChessBoardController
        // 这里暂时不直接调用，因为GameScoreController不应该直接依赖ChessBoardController
        // 实际的调用在GamePageController.updatePlayerGameScore中完成
        console.log("player_game_pfb\u66F4\u65B0\u903B\u8F91\u7531GamePageController\u5904\u7406");
    };
    __decorate([
        property(cc.Node)
    ], GameScoreController.prototype, "scoreLayout", void 0);
    __decorate([
        property(cc.Prefab)
    ], GameScoreController.prototype, "playerScorePfb", void 0);
    GameScoreController = __decorate([
        ccclass
    ], GameScoreController);
    return GameScoreController;
}(cc.Component));
exports.default = GameScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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