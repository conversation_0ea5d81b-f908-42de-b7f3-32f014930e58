
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/BtnController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0cfc6AzwvpKzLNWGyxSfqHD', 'BtnController');
// scripts/game/BtnController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var LocalStorageManager_1 = require("../util/LocalStorageManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var BtnController = /** @class */ (function (_super) {
    __extends(BtnController, _super);
    function BtnController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.settingBtn = null; // 设置按钮（固定不动）
        _this.settingPanel = null; // 设置面板容器
        _this.maskNode = null; // 遮罩节点，用于控制显示区域
        _this.gameplayBtn = null; // 玩法按钮
        _this.musicBtn = null; // 音乐开关按钮
        _this.soundBtn = null; // 音效开关按钮
        _this.infoDialogNode = null; // info_dialog节点引用
        // 音乐和音效开关状态
        _this.music = true;
        _this.sound = true;
        // 设置面板展开状态
        _this.isSettingExpanded = false;
        // 动画时长 - 可以调整这个值来控制动画速度
        _this.animationDuration = 0.5; // 增加到0.5秒，让动画更缓慢
        // 保存遮罩的原始高度
        _this.originalMaskHeight = 0;
        // 防止重复点击的标志
        _this.isAnimating = false;
        // 防止重复初始化音乐的标志
        _this.isMusicInitialized = false;
        // 添加初始化标志，防止重复初始化
        _this.hasInitialized = false;
        return _this;
        // update (dt) {}
    }
    // LIFE-CYCLE CALLBACKS:
    BtnController.prototype.onLoad = function () {
        // 从本地存储获取音乐和音效开关状态
        this.music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch();
        this.sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch();
        // 初始化设置面板为隐藏状态
        this.initSettingPanel();
    };
    BtnController.prototype.start = function () {
        var _this = this;
        // 预加载所有按钮资源，然后再设置按钮事件
        this.preloadButtonResources(function () {
            // 添加一个小延迟，确保所有资源都完全就绪
            _this.scheduleOnce(function () {
                // 设置主设置按钮点击事件 - 使用新的按压UI样式，播放音效
                _this.setupImageButton(_this.settingBtn, 'side_btn_menu_normal', 'side_btn_menu_pressed', function () {
                    _this.toggleSettingPanel();
                }, true); // 改为true，播放音效来确认点击是否真的被执行
                // 设置玩法按钮点击事件 - 打开info_dialog页面
                _this.setupImageButton(_this.gameplayBtn, 'side_btn_info_normal', 'side_btn_info_pressed', function () {
                    _this.openInfoDialog();
                });
                // 设置音乐按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupMusicButton();
                // 设置音效按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupSoundButton();
                // 初始化按钮状态 - 使用新的按钮UI样式
                _this.updateMusicButtonUI();
                _this.updateSoundButtonUI();
                // 立即检查按钮状态和确保按钮在最上层
                _this.checkButtonStates();
                _this.ensureButtonsOnTop();
            }, 0.2); // 等待200毫秒
        });
        // 只在首次加载时播放音乐，避免重置正在播放的音乐
        this.scheduleOnce(function () {
            _this.initializeMusicPlayback();
        }, 0.1);
    };
    // 确保所有按钮都在最上层
    BtnController.prototype.ensureButtonsOnTop = function () {
        var buttons = [this.settingBtn, this.gameplayBtn, this.musicBtn, this.soundBtn];
        buttons.forEach(function (btn, index) {
            if (btn) {
                // 设置合理的zIndex值，确保按钮在所有棋盘和UI元素之上
                btn.zIndex = cc.macro.MAX_ZINDEX - 10 + index;
            }
        });
        // 如果有设置面板，确保它的zIndex也足够高，但低于按钮
        if (this.settingPanel) {
            this.settingPanel.zIndex = cc.macro.MAX_ZINDEX - 20;
        }
        if (this.maskNode) {
            this.maskNode.zIndex = cc.macro.MAX_ZINDEX - 19;
        }
    };
    // 检查按钮状态的调试方法
    BtnController.prototype.checkButtonStates = function () {
        var buttons = [
            { name: "设置按钮", node: this.settingBtn },
            { name: "玩法按钮", node: this.gameplayBtn },
            { name: "音乐按钮", node: this.musicBtn },
            { name: "音效按钮", node: this.soundBtn }
        ];
        buttons.forEach(function (btn) {
            if (btn.node) {
            }
            else {
                console.error("BtnController: " + btn.name + "\u8282\u70B9\u672A\u8BBE\u7F6E");
            }
        });
    };
    /**
     * 设置图片按钮 - 支持按压状态切换
     */
    BtnController.prototype.setupImageButton = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        if (playSound === void 0) { playSound = true; }
        if (!node) {
            console.error("BtnController: setupImageButton - 节点为空", normalImg);
            return;
        }
        // 确保节点可以接收触摸事件
        node.active = true;
        // 将按钮移动到最上层，确保不被其他UI元素遮挡
        node.zIndex = cc.macro.MAX_ZINDEX - 5;
        // 检查是否有Button组件阻挡触摸事件
        var buttonComponent = node.getComponent(cc.Button);
        if (buttonComponent) {
            buttonComponent.enabled = false;
        }
        // 强制确保节点具有正确的触摸属性
        node._touchListener = null; // 清除可能存在的旧监听器
        // 设置初始状态为normal（使用预加载的资源，应该能立即设置成功）
        this.setButtonSprite(node, normalImg);
        // 延迟一帧注册触摸事件，确保图片设置完成
        this.scheduleOnce(function () {
            _this.registerButtonEvents(node, normalImg, pressedImg, clickCallback, playSound);
            // 再次确保按钮在最上层
            node.zIndex = cc.macro.MAX_ZINDEX - 5;
        }, 0.1);
    };
    // 注册按钮触摸事件
    BtnController.prototype.registerButtonEvents = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        // 清除之前的事件监听器，避免重复注册
        node.off(cc.Node.EventType.TOUCH_START);
        node.off(cc.Node.EventType.TOUCH_END);
        node.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加触摸事件
        node.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时切换到pressed状态
            _this.setButtonSprite(node, pressedImg);
        }, this);
        node.on(cc.Node.EventType.TOUCH_END, function () {
            // 抬起时切换回normal状态并执行点击回调
            _this.setButtonSprite(node, normalImg);
            // 根据参数决定是否播放按钮点击音效
            if (playSound) {
                _this.playButtonClickSound();
            }
            if (clickCallback) {
                clickCallback();
            }
        }, this);
        node.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时也要切换回normal状态
            _this.setButtonSprite(node, normalImg);
        }, this);
    };
    /**
     * 设置按钮精灵图片 - 优先使用预加载的缓存资源
     */
    BtnController.prototype.setButtonSprite = function (node, imageName) {
        if (!node || !imageName) {
            console.error("BtnController: setButtonSprite - 参数无效", { node: !!node, imageName: imageName });
            return;
        }
        // 使用Config中定义的按钮资源路径，不包含扩展名
        var imagePath = Config_1.Config.buttonRes + imageName;
        // 优先从缓存获取（预加载的资源应该已经在缓存中）
        var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
        if (cachedSpriteFrame) {
            // 图片已经在缓存中，直接使用
            var sprite = node.getComponent(cc.Sprite);
            if (sprite) {
                sprite.spriteFrame = cachedSpriteFrame;
                node.color = cc.Color.WHITE;
                node.opacity = 255;
                return; // 成功设置，直接返回
            }
            else {
                console.error("BtnController: 节点没有Sprite组件", node.name);
                return;
            }
        }
        // 如果缓存中没有，说明预加载可能失败了，进行备用加载
        console.warn("BtnController: 缓存中没有找到图片，进行备用加载", imagePath);
        cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
            if (!error && spriteFrame && node && node.isValid) {
                var sprite = node.getComponent(cc.Sprite);
                if (sprite) {
                    sprite.spriteFrame = spriteFrame;
                    node.color = cc.Color.WHITE;
                    node.opacity = 255;
                }
                else {
                    console.error("BtnController: 节点缺少Sprite组件", node.name);
                }
            }
            else {
                console.error("BtnController: 备用加载按钮图片失败", {
                    imagePath: imagePath,
                    nodeValid: node && node.isValid,
                    error: error ? error.message : "未知错误"
                });
            }
        });
    };
    /**
     * 清理按钮状态 - 移除可能导致高亮效果的设置
     */
    BtnController.prototype.cleanButtonState = function (node) {
        if (!node)
            return;
        // 重置节点颜色和透明度
        node.color = cc.Color.WHITE;
        node.opacity = 255;
    };
    /**
     * 设置音乐按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupMusicButton = function () {
        var _this = this;
        if (!this.musicBtn)
            return;
        // 更新按钮显示状态
        this.updateMusicButtonUI();
        // 添加触摸事件
        this.musicBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.music ? 'side_btn_music(on)_pressed' : 'side_btn_music(off)_pressed';
            _this.setButtonSprite(_this.musicBtn, currentImg);
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音乐状态 - 模仿SettingDialogController的实现
            _this.music = !_this.music;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setMusicSwitch(_this.music);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
            _this.setButtonSprite(_this.musicBtn, newImgName);
            // 直接控制音乐播放，模仿SettingDialogController
            if (_this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
            else {
                AudioManager_1.AudioManager.stopBgm();
            }
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateMusicButtonUI();
        }, this);
    };
    /**
     * 设置音效按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupSoundButton = function () {
        var _this = this;
        if (!this.soundBtn)
            return;
        // 更新按钮显示状态
        this.updateSoundButtonUI();
        // 添加触摸事件
        this.soundBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.sound ? 'side_btn_sound(on)_pressed' : 'side_btn_sound(off)_pressed';
            _this.setButtonSprite(_this.soundBtn, currentImg);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音效状态
            _this.sound = !_this.sound;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setSoundSwitch(_this.sound);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
            _this.setButtonSprite(_this.soundBtn, newImgName);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateSoundButtonUI();
        }, this);
    };
    /**
     * 更新音乐按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateMusicButtonUI = function () {
        if (!this.musicBtn)
            return;
        var imgName = this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
        this.setButtonSprite(this.musicBtn, imgName);
    };
    /**
     * 更新音效按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateSoundButtonUI = function () {
        if (!this.soundBtn)
            return;
        var imgName = this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
        this.setButtonSprite(this.soundBtn, imgName);
    };
    /**
     * 播放按钮点击音效 - 模仿项目中的音效播放方式
     */
    BtnController.prototype.playButtonClickSound = function () {
        // 检查音效开关状态，只有在音效开启时才播放
        if (this.sound) {
            // 使用AudioManager的按键音效方法
            AudioManager_1.AudioManager.keyingToneAudio();
        }
    };
    /**
     * 初始化音乐播放 - 只在首次加载时播放，避免重置
     */
    BtnController.prototype.initializeMusicPlayback = function () {
        // 只在未初始化时播放音乐
        if (!this.isMusicInitialized) {
            this.isMusicInitialized = true;
            // 检查音乐开关状态，只有开启时才播放
            if (this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
        }
    };
    /**
     * 打开info_dialog页面 - 模仿项目中的实现方式
     */
    BtnController.prototype.openInfoDialog = function () {
        // 如果动画正在播放，禁止操作
        if (this.isAnimating) {
            return;
        }
        // 自动隐藏maskNode（收起设置面板）
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        // 模仿HallPageController中的实现方式
        if (this.infoDialogNode) {
            var infoDialogController = this.infoDialogNode.getComponent("InfoDialogController");
            if (infoDialogController) {
                // 调用show方法，传入空的回调函数
                infoDialogController.show(function () { });
            }
        }
    };
    /**
     * 预加载所有按钮资源 - 确保按钮图片在设置事件前就已加载完成
     */
    BtnController.prototype.preloadButtonResources = function (callback) {
        // 定义所有需要预加载的按钮资源
        var buttonImages = [
            'side_btn_menu_normal',
            'side_btn_menu_pressed',
            'side_btn_info_normal',
            'side_btn_info_pressed',
            'side_btn_music(on)_normal',
            'side_btn_music(on)_pressed',
            'side_btn_music(off)_normal',
            'side_btn_music(off)_pressed',
            'side_btn_sound(on)_normal',
            'side_btn_sound(on)_pressed',
            'side_btn_sound(off)_normal',
            'side_btn_sound(off)_pressed'
        ];
        var loadedCount = 0;
        var totalCount = buttonImages.length;
        // 如果没有需要加载的资源，直接回调
        if (totalCount === 0) {
            callback();
            return;
        }
        // 预加载每个资源
        buttonImages.forEach(function (imageName) {
            var imagePath = Config_1.Config.buttonRes + imageName;
            // 先检查是否已经在缓存中
            var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
            if (cachedSpriteFrame) {
                loadedCount++;
                if (loadedCount >= totalCount) {
                    callback();
                }
            }
            else {
                // 加载资源
                cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
                    if (!error && spriteFrame) {
                    }
                    else {
                        console.error("BtnController: \u6309\u94AE\u8D44\u6E90\u52A0\u8F7D\u5931\u8D25 [" + (loadedCount + 1) + "/" + totalCount + "]", imageName, error ? error.message : "未知错误");
                    }
                    loadedCount++;
                    if (loadedCount >= totalCount) {
                        callback();
                    }
                });
            }
        });
    };
    /**
     * 初始化设置面板状态
     */
    BtnController.prototype.initSettingPanel = function () {
        // 如果已经初始化过，跳过
        if (this.hasInitialized) {
            return;
        }
        if (this.settingPanel) {
            // 设置面板初始为隐藏状态
            this.settingPanel.active = false;
        }
        // 重要：设置mask节点的初始状态为隐藏（高度为0）
        if (this.maskNode) {
            this.maskNode.height = 0;
            this.maskNode.opacity = 255;
        }
        // 设置初始状态
        this.isSettingExpanded = false;
        this.isAnimating = false;
        this.hasInitialized = true;
    };
    /**
     * 切换设置面板的展开/收起状态
     */
    BtnController.prototype.toggleSettingPanel = function () {
        // 防止动画进行中的重复点击
        if (this.isAnimating) {
        }
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        else {
            this.showSettingPanel();
        }
    };
    /**
     * 展开设置面板 - mask的size.y从0缓慢增加到275
     */
    BtnController.prototype.showSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            console.error("BtnController: showSettingPanel - maskNode为空");
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = true;
        // 显示settingPanel
        if (this.settingPanel) {
            this.settingPanel.active = true;
        }
        // 设置mask初始状态：高度为0，透明度为正常
        this.maskNode.height = 0;
        this.maskNode.opacity = 255;
        // 确保按钮始终在最上层
        this.ensureButtonsOnTop();
        // 执行展开动画 - mask高度从0到275
        cc.tween(this.maskNode)
            .to(this.animationDuration, {
            height: 275
        }, {
            easing: 'quartOut'
        })
            .call(function () {
            // 动画完成，解除动画锁定
            _this.isAnimating = false;
        })
            .start();
    };
    /**
     * 收起设置面板 - mask的size.y从275缓慢减少到0
     */
    BtnController.prototype.hideSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = false;
        // 执行收起动画 - mask高度从275缓慢减少到0，同时添加渐隐效果
        cc.tween(this.maskNode)
            .to(this.animationDuration * 0.7, {
            height: 0
        }, {
            easing: 'quartOut'
        })
            .to(this.animationDuration * 0.3, {
            opacity: 0
        }, {
            easing: 'quartIn' // 最后阶段快速渐隐
        })
            .call(function () {
            // 动画完成后隐藏面板，解除动画锁定，恢复透明度
            if (_this.settingPanel) {
                _this.settingPanel.active = false;
            }
            _this.maskNode.opacity = 255; // 恢复透明度，为下次展开做准备
            _this.isAnimating = false;
            // 确保按钮始终在最上层
            _this.ensureButtonsOnTop();
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingPanel", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "maskNode", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "gameplayBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "musicBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "soundBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "infoDialogNode", void 0);
    BtnController = __decorate([
        ccclass
    ], BtnController);
    return BtnController;
}(cc.Component));
exports.default = BtnController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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