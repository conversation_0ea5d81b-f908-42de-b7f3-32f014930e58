
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/CongratsDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f3b5eIOZ4tF8YT//S1WoI5Z', 'CongratsDialogController');
// scripts/game/CongratsDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var CongratsItemController_1 = require("../pfb/CongratsItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//结算页面
var CongratsDialogController = /** @class */ (function (_super) {
    __extends(CongratsDialogController, _super);
    function CongratsDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.contentLay = null;
        _this.backBtn = null;
        _this.congratsItem = null; //列表的 item
        _this.layoutNode = null; //存放列表的布局
        _this.countdownTimeLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.backCallback = null; //隐藏弹窗的回调
        _this.seconds = 10; //倒计时 10 秒
        return _this;
    }
    CongratsDialogController.prototype.onLoad = function () {
        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);
    };
    CongratsDialogController.prototype.onEnable = function () {
        this.updateCountdownLabel(this.seconds);
        Tools_1.Tools.setCountDownTimeLabel(this.backBtn);
    };
    CongratsDialogController.prototype.start = function () {
        var _this = this;
        //backBtn 按钮点击事件
        Tools_1.Tools.greenButton(this.backBtn, function () {
            _this.hide(true);
        });
    };
    CongratsDialogController.prototype.show = function (noticeSettlement, backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        this._setData(noticeSettlement);
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    CongratsDialogController.prototype._setData = function (noticeSettlement) {
        var index = noticeSettlement.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
        GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = noticeSettlement.users[index].coin; //更新自己的最新金币
        this.layoutNode.removeAllChildren();
        var _loop_1 = function (i) {
            var item = cc.instantiate(this_1.congratsItem);
            var data = noticeSettlement.users[i];
            this_1.layoutNode.addChild(item);
            setTimeout(function () {
                item.getComponent(CongratsItemController_1.default).createData(data, GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users);
            }, 100);
        };
        var this_1 = this;
        for (var i = 0; i < noticeSettlement.users.length; ++i) {
            _loop_1(i);
        }
        this.startCountdown(10); //倒计时 10 秒
    };
    // bool 在隐藏的时候是否返回大厅
    CongratsDialogController.prototype.hide = function (bool) {
        var _this = this;
        if (bool === void 0) { bool = false; }
        if (this.backCallback) {
            this.backCallback();
        }
        GameMgr_1.GameMgr.Console.Log('隐藏结算页面');
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
            if (bool) {
                GlobalBean_1.GlobalBean.GetInstance().cleanData();
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                    'data': { 'type': 2 } //2是结算弹窗跳转的
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        })
            .start();
    };
    CongratsDialogController.prototype.onDisable = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
    };
    CongratsDialogController.prototype.startCountdown = function (seconds) {
        var _this = this;
        var remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                // 倒计时结束时的处理逻辑
                _this.hide(true);
                return;
            }
            _this.updateCountdownLabel(remainingSeconds);
        }, 1000);
    };
    CongratsDialogController.prototype.updateCountdownLabel = function (seconds) {
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = "\uFF08" + seconds + "s\uFF09";
        }
    };
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "backBtn", void 0);
    __decorate([
        property(cc.Prefab)
    ], CongratsDialogController.prototype, "congratsItem", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "layoutNode", void 0);
    CongratsDialogController = __decorate([
        ccclass
    ], CongratsDialogController);
    return CongratsDialogController;
}(cc.Component));
exports.default = CongratsDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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