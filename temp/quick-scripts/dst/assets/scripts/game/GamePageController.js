
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //棋盘控制器
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        var index = noticeSettlement.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索 
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (noticeSettlement.users[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1 && data.validHexCoords) {
            // 六边形地图，暂时使用默认值，后续可根据实际需求调整
            this.currentMineCount = Math.floor(data.validHexCoords.length * 0.15); // 约15%的格子是炸弹
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.chessBoardController) {
            this.chessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        var _this = this;
        var receiveTime = new Date().toLocaleTimeString();
        console.log("\u6536\u5230NoticeActionDisplay\u65F6\u95F4: " + receiveTime + ", \u5012\u8BA1\u65F6: " + (data.countDown || 5) + "\u79D2");
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 在棋盘上显示所有玩家的操作
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
        // 使用setTimeout作为备选方案
        setTimeout(function () {
            _this.updateBoardAfterActions(data);
        }, 1000);
        // 同时也使用scheduleOnce
        this.scheduleOnce(this.delayedUpdateBoard.bind(this, data), 1.0);
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 第一步：先让所有头像消失
        var _this = this;
        this.hideAllAvatars(data.playerActions, function () {
            // 头像消失完成后，执行后续操作
            // 第二步：处理每个玩家的操作结果
            data.playerActions.forEach(function (action) {
                _this.processPlayerActionResult(action);
            });
            // 第三步：处理连锁展开结果
            if (data.floodFillResults && data.floodFillResults.length > 0) {
                data.floodFillResults.forEach(function (floodFill) {
                    _this.processFloodFillResult(floodFill);
                });
            }
        });
    };
    /**
     * 让所有头像消失（简化版：直接删除所有头像）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 直接调用一次头像删除，不区分位置
        this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
            onComplete();
        });
    };
    /**
     * 处理单个玩家操作结果
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            this.chessBoardController.createBoomPrefab(x, y);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 为每个连锁格子播放消失动画
        floodFill.revealedBlocks.forEach(function (block, index) {
            // 延迟播放动画，创造连锁效果
            _this.scheduleOnce(function () {
                _this.chessBoardController.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
            }, index * 0.1); // 每个格子间隔0.1秒
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        console.log("\u6536\u5230NoticeRoundEnd\uFF0C\u670D\u52A1\u5668\u5012\u8BA1\u65F6: " + data.countDown + "\u79D2");
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 同步服务器的倒计时时间
        var serverCountdown = data.countDown || 0;
        if (serverCountdown > 0) {
            // 如果服务器还有倒计时，继续倒计时
            console.log("\u670D\u52A1\u5668\u8FD8\u6709" + serverCountdown + "\u79D2\uFF0C\u7EE7\u7EED\u5012\u8BA1\u65F6");
            this.currentCountdown = serverCountdown;
            this.updateCountdownDisplay(this.currentCountdown);
            this.startCountdown(this.currentCountdown);
        }
        else {
            // 如果服务器倒计时为0，停止倒计时
            console.log("\u670D\u52A1\u5668\u5012\u8BA1\u65F6\u7ED3\u675F\uFF0C\u505C\u6B62\u5BA2\u6237\u7AEF\u5012\u8BA1\u65F6");
            this.clearCountdownTimer();
            this.updateCountdownDisplay(0);
        }
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.chessBoardController) {
            this.chessBoardController.clearAllPlayerNodes();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        if (!this.chessBoardController || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
        if (playerTotalScores) {
            this.displayPlayerScoreAnimationsAndUpdateTotalScores(playerActions, playerTotalScores);
        }
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.chessBoardController) {
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.chessBoardController) {
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.chessBoardController) {
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("ChessBoardController未设置，无法显示player_game_pfb加分效果");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        console.log("\u5F00\u59CB\u5012\u8BA1\u65F6: " + seconds + "\u79D2");
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            console.log("\u5012\u8BA1\u65F6\u66F4\u65B0: " + remainingSeconds + "\u79D2");
            _this.updateCountdownDisplay(remainingSeconds);
            if (remainingSeconds < 0) { // 改为 < 0，让1秒能完整显示
                console.log("\u5012\u8BA1\u65F6\u7ED3\u675F\uFF0C\u6E05\u9664\u5B9A\u65F6\u5668");
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            if (seconds >= 1) {
                this.timeLabel.string = "" + seconds;
                console.log("\u663E\u793A\u5012\u8BA1\u65F6: " + seconds);
            }
            else {
                this.timeLabel.string = ""; // 不显示0或负数，显示空白
                console.log("\u5012\u8BA1\u65F6\u7ED3\u675F\uFF0C\u663E\u793A\u7A7A\u767D");
            }
        }
        this.currentCountdown = seconds;
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            console.log("\u6E05\u9664\u5012\u8BA1\u65F6\u5B9A\u65F6\u5668");
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        console.log("\u5F53\u524D\u7528\u6237: " + currentUserId + ", \u5148\u624B\u73A9\u5BB6: " + (firstChoicePlayer === null || firstChoicePlayer === void 0 ? void 0 : firstChoicePlayer.userId) + ", \u6211\u662F\u5426\u5148\u624B: " + isCurrentUserFirstChoice);
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                console.log("\u4E3A\u5148\u624B\u73A9\u5BB6 " + firstChoicePlayer.userId + " \u5728\u5206\u6570\u9762\u677F\u663E\u793A+1");
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else {
            console.warn("chessBoardController 不存在，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            console.log("\u5148\u624B\u73A9\u5BB6 " + action.userId + " \u5728\u5206\u6570\u9762\u677F\u663E\u793A\u672C\u56DE\u5408\u5206\u6570: " + action.score);
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
    };
    GamePageController.prototype.onDestroy = function () {
        // 移除棋盘点击事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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