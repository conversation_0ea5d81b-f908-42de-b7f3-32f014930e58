
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":8,"./assets/meshTools/BaseSDK":19,"./assets/meshTools/tools/MeshSdkApi":3,"./assets/meshTools/tools/Publish":78,"./assets/meshTools/tools/MeshSdk":21,"./assets/scripts/TipsDialogController":15,"./assets/scripts/ToastController":34,"./assets/scripts/GlobalManagerController":17,"./assets/scripts/bean/GameBean":16,"./assets/scripts/bean/GlobalBean":4,"./assets/scripts/bean/LanguageType":33,"./assets/scripts/bean/EnumBean":20,"./assets/scripts/common/GameData":23,"./assets/scripts/common/GameMgr":9,"./assets/scripts/common/GameTools":65,"./assets/scripts/common/MineConsole":18,"./assets/scripts/common/EventCenter":30,"./assets/scripts/game/ChessBoardController1":24,"./assets/scripts/game/CongratsDialogController":22,"./assets/scripts/game/GamePageController":25,"./assets/scripts/game/GameScoreController":26,"./assets/scripts/game/BtnController":27,"./assets/scripts/game/Chess/GridController":35,"./assets/scripts/game/Chess/ChessBoardController":5,"./assets/scripts/hall/HallCenterLayController":28,"./assets/scripts/hall/HallCreateRoomController":29,"./assets/scripts/hall/HallJoinRoomController":31,"./assets/scripts/hall/HallPageController":50,"./assets/scripts/hall/HallParentController":55,"./assets/scripts/hall/InfoDialogController":32,"./assets/scripts/hall/KickOutDialogController":43,"./assets/scripts/hall/LeaveDialogController":36,"./assets/scripts/hall/LevelSelectDemo":38,"./assets/scripts/hall/MatchParentController":37,"./assets/scripts/hall/PlayerLayoutController":39,"./assets/scripts/hall/SettingDialogController":40,"./assets/scripts/hall/TopUpDialogController":47,"./assets/scripts/hall/HallAutoController":41,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/scripts/hall/Level/LevelSelectDemoInLevel":44,"./assets/scripts/hall/Level/LevelSelectExample":42,"./assets/scripts/hall/Level/LevelSelectPageController":45,"./assets/scripts/hall/Level/LevelSelectTest":46,"./assets/scripts/hall/Level/ScrollViewHelper":63,"./assets/scripts/hall/Level/LevelItemController":48,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/net/HttpManager":54,"./assets/scripts/net/HttpUtils":49,"./assets/scripts/net/IHttpMsgBody":51,"./assets/scripts/net/MessageBaseBean":52,"./assets/scripts/net/MessageId":53,"./assets/scripts/net/WebSocketManager":61,"./assets/scripts/net/WebSocketTool":64,"./assets/scripts/net/ErrorCode":60,"./assets/scripts/pfb/InfoItemController":59,"./assets/scripts/pfb/InfoItemOneController":11,"./assets/scripts/pfb/MatchItemController":57,"./assets/scripts/pfb/PlayerGameController ":56,"./assets/scripts/pfb/PlayerScoreController":77,"./assets/scripts/pfb/SeatItemController":58,"./assets/scripts/pfb/CongratsItemController":62,"./assets/scripts/start_up/StartUpPageController":76,"./assets/scripts/start_up/StartUpCenterController":12,"./assets/scripts/test/NoticeRoundStartTest":13,"./assets/scripts/util/AudioMgr":14,"./assets/scripts/util/BlockingQueue":66,"./assets/scripts/util/Config":72,"./assets/scripts/util/Dictionary":73,"./assets/scripts/util/LocalStorageManager":67,"./assets/scripts/util/NickNameLabel":74,"./assets/scripts/util/Tools":69,"./assets/scripts/util/AudioManager":68,"./assets/meshTools/MeshTools":70,"./assets/resources/i18n/zh_HK":7,"./assets/resources/i18n/en":71,"./assets/resources/i18n/zh_CN":75},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{"../MeshTools":70,"../BaseSDK":19,"../../scripts/net/MessageBaseBean":52,"../../scripts/common/GameMgr":9,"../../scripts/common/EventCenter":30,"MeshSdk":21},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":41},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":56},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"./ScrollViewHelper":63},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":3,"./EventCenter":30,"./GameData":23,"./GameTools":65,"./MineConsole":18},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../common/EventCenter":30,"../common/GameMgr":9,"../net/MessageBaseBean":52,"../util/Config":72},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"../common/GameMgr":9,"../common/EventCenter":30,"../net/MessageId":53},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"./Config":72,"./Dictionary":73},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"./util/Config":72,"./util/Tools":69},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{"./TipsDialogController":15,"./ToastController":34,"../meshTools/MeshTools":70,"../meshTools/tools/Publish":78,"./bean/GlobalBean":4,"./bean/LanguageType":33,"./bean/EnumBean":20,"./common/GameMgr":9,"./common/EventCenter":30,"./game/GamePageController":25,"./hall/TopUpDialogController":47,"./hall/HallPageController":50,"./net/GameServerUrl":10,"./net/MessageBaseBean":52,"./net/MessageId":53,"./net/WebSocketManager":61,"./net/WebSocketTool":64,"./net/ErrorCode":60,"./start_up/StartUpPageController":76,"./util/Config":72,"./util/AudioMgr":14},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{"../bean/GlobalBean":4,"../common/EventCenter":30,"../common/GameMgr":9,"../net/MessageBaseBean":52,"../pfb/CongratsItemController":62,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../../meshTools/MeshTools":70,"../../meshTools/Singleton":8,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/ChessBoardController1.js"},{"deps":{"./CongratsDialogController":22,"./GameScoreController":26,"../bean/GlobalBean":4,"../hall/LeaveDialogController":36,"../util/Config":72,"../util/Tools":69,"../util/AudioManager":68,"./Chess/ChessBoardController":5,"../net/MessageId":53,"../net/WebSocketManager":61},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/PlayerScoreController":77},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../util/AudioManager":68,"../util/Config":72,"../util/LocalStorageManager":67},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../bean/GlobalBean":4,"../net/MessageId":53,"../net/WebSocketManager":61,"../ToastController":34,"./HallAutoController":41,"./HallCreateRoomController":29,"./HallJoinRoomController":31},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/SeatItemController":58,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":9},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../pfb/InfoItemController":59,"../pfb/InfoItemOneController":11,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"../common/GameMgr":9,"../net/MessageId":53,"../net/WebSocketManager":61,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../../meshTools/tools/Publish":78,"../bean/GlobalBean":4,"../common/EventCenter":30,"../common/GameMgr":9,"../net/MessageBaseBean":52,"../pfb/MatchItemController":57,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../bean/GlobalBean":4,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../../meshTools/tools/Publish":78,"../util/AudioManager":68,"../util/Config":72,"../util/LocalStorageManager":67,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../net/MessageId":53,"../net/WebSocketManager":61,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectDemoInLevel.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"./LevelSelectController":6,"./ScrollViewHelper":63},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectTest.js"},{"deps":{"../common/GameMgr":9,"../util/Config":72,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{"../bean/GlobalBean":4,"../common/GameMgr":9,"../net/MessageId":53,"../net/WebSocketManager":61,"../net/WebSocketTool":64,"../ToastController":34,"../util/AudioManager":68,"./HallParentController":55,"./InfoDialogController":32,"./KickOutDialogController":43,"./LeaveDialogController":36,"./MatchParentController":37,"./SettingDialogController":40},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"./HttpUtils":49,"./MessageBaseBean":52,"./GameServerUrl":10,"../../meshTools/MeshTools":70,"../common/GameMgr":9,"../common/EventCenter":30},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{"../../meshTools/tools/Publish":78,"../bean/GlobalBean":4,"../common/GameMgr":9,"../net/MessageId":53,"../net/WebSocketManager":61,"../ToastController":34,"../util/Config":72,"../util/Tools":69,"./HallCenterLayController":28},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../util/NickNameLabel":74,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../util/NickNameLabel":74,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":30,"../common/GameMgr":9,"./WebSocketTool":64},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{"../../meshTools/tools/Publish":78,"../util/Config":72,"../util/NickNameLabel":74,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./MessageBaseBean":52,"./MessageId":53,"../util/Tools":69,"../../meshTools/Singleton":8,"../common/EventCenter":30,"../common/GameMgr":9},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{"./AudioMgr":14,"./LocalStorageManager":67},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"./AudioManager":68,"./Config":72},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"./tools/Publish":78},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"../common/GameMgr":9,"./StartUpCenterController":12},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"../bean/GlobalBean":4,"../util/NickNameLabel":74,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    