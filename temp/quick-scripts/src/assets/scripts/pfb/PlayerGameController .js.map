{"version": 3, "sources": ["assets/scripts/pfb/PlayerGameController .ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,uCAAsC;AAEhC,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAAkD,wCAAY;IAA9D;QAAA,qEA2OC;QAxOG,YAAM,GAAY,IAAI,CAAC,CAAG,IAAI;QAE9B,cAAQ,GAAY,IAAI,CAAC,CAAE,MAAM;QAEjC,kBAAY,GAAY,IAAI,CAAC,CAAE,iBAAiB;QAEhD,kBAAY,GAAY,IAAI,CAAC,CAAE,oBAAoB;;QAiOnD,iBAAiB;IACrB,CAAC;IAhOG,0CAA0C;IAC1C,qDAAqD;IACrD,+CAA+C;IAC/C,gEAAgE;IAIhE,eAAe;IAEf,oCAAK,GAAL;QACI,6DAA6D;QAC7D,uBAAuB;QAEvB,eAAe;QACf,wEAAwE;QACxE,oEAAoE;QACpE,0EAA0E;QAE1E,yCAAyC;QACzC,eAAe;QACf,uBAAuB;QACvB,oCAAoC;QACpC,IAAI;IACR,CAAC;IAED,sCAAO,GAAP,UAAQ,IAAc;QAAtB,iBASC;QARG,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,IAAI,IAAI,IAAI,EAAE;gBACd,KAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;aAC9B;iBAAM;gBACH,aAAK,CAAC,qBAAqB,CAAC,KAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA,MAAM;gBAC5D,KAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,qDAAqD;IAErD,eAAe;IACf,sDAAsD;IACtD,kCAAkC;IAClC,+BAA+B;IAC/B,gBAAgB;IAChB,+CAA+C;IAC/C,IAAI;IAEJ,eAAe;IACf,mDAAmD;IACnD,mCAAmC;IACnC,4CAA4C;IAC5C,gCAAgC;IAChC,wDAAwD;IACxD,6BAA6B;IAC7B,QAAQ;IACR,IAAI;IAEJ,eAAe;IACf,uDAAuD;IACvD,mCAAmC;IACnC,4CAA4C;IAC5C,IAAI;IAEJ,aAAa;IACb,uCAAuC;IACvC,iCAAiC;IACjC,qCAAqC;IACrC,2BAA2B;IAC3B,6DAA6D;IAC7D,kCAAkC;IAClC,2CAA2C;IAC3C,oDAAoD;IACpD,YAAY;IACZ,QAAQ;IACR,IAAI;IAEJ,4BAA4B;IAC5B,8CAA8C;IAC9C,mCAAmC;IACnC,gBAAgB;IAChB,0CAA0C;IAC1C,sEAAsE;IACtE,uBAAuB;IACvB,sDAAsD;IACtD,4CAA4C;IAC5C,qBAAqB;IACrB,gFAAgF;IAChF,2DAA2D;IAC3D,oDAAoD;IACpD,QAAQ;IACR,gBAAgB;IAChB,iDAAiD;IACjD,oCAAoC;IACpC,IAAI;IAEJ,2BAA2B;IAC3B,0BAA0B;IAC1B,kCAAkC;IAClC,yBAAyB;IACzB,gDAAgD;IAChD,uBAAuB;IACvB,sDAAsD;IACtD,4BAA4B;IAC5B,mEAAmE;IACnE,6CAA6C;IAC7C,oBAAoB;IACpB,gFAAgF;IAChF,2DAA2D;IAC3D,mDAAmD;IACnD,oCAAoC;IACpC,eAAe;IACf,4CAA4C;IAC5C,QAAQ;IACR,gBAAgB;IAChB,iDAAiD;IACjD,mCAAmC;IACnC,IAAI;IAIJ;;;OAGG;IACH,2CAAY,GAAZ,UAAa,QAAgB;QAA7B,iBAsDC;QArDG,OAAO,CAAC,GAAG,CAAC,yCAAuC,QAAU,CAAC,CAAC;QAE/D,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,4BAA4B;YAC5B,IAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC1E,IAAI,gBAAgB,EAAE;gBAClB,IAAM,cAAc,GAAG,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC/D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBAClD,OAAO,CAAC,GAAG,CAAC,sDAAgC,cAAc,CAAC,MAAQ,CAAC,CAAC;iBACxE;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;aAC9D;YAED,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAE7C,UAAU;YACV,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE5C,SAAS;YACT,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC;YAE9B,SAAS;YACT,IAAI,WAAS,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YAEpC,gBAAgB;YAChB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;iBACtB,QAAQ,CACL,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EACrC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EACnC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CACjC;iBACA,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;iBACvB,KAAK,CAAC,GAAG,CAAC;iBACV,QAAQ,CACL,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EACnC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EACnC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAChC;iBACA,IAAI,CAAC;gBACF,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;gBACjC,KAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,CAAC;gBAChC,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC;gBAC9B,KAAI,CAAC,YAAY,CAAC,CAAC,GAAG,WAAS,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC/C,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;SAChB;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACH,2CAAY,GAAZ,UAAa,QAAgB;QAA7B,iBA4BC;QA3BG,OAAO,CAAC,GAAG,CAAC,yCAAuC,QAAU,CAAC,CAAC;QAE/D,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,4BAA4B;YAC5B,IAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC1E,IAAI,gBAAgB,EAAE;gBAClB,IAAM,cAAc,GAAG,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC/D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBAClD,OAAO,CAAC,GAAG,CAAC,sDAAgC,cAAc,CAAC,MAAQ,CAAC,CAAC;iBACxE;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;aAC9D;YAED,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;YAEhC,QAAQ;YACR,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,KAAI,CAAC,YAAY,EAAE;oBACnB,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;oBACjC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBAC/C;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;SACX;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,UAAU;IACV,+CAAgB,GAAhB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SACpC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IArOD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACO;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACW;IATZ,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CA2OxC;IAAD,2BAAC;CA3OD,AA2OC,CA3OiD,EAAE,CAAC,SAAS,GA2O7D;kBA3OoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser } from \"../bean/GameBean\";\nimport { Tools } from \"../util/Tools\";\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class PlayerGameController extends cc.Component {\n\n    @property(cc.Node)\n    avatar: cc.Node = null;   //头像\n    @property(cc.Node)\n    flagNode: cc.Node = null;  //旗子节点\n    @property(cc.Node)\n    addScoreNode: cc.Node = null;  //加分背景节点 addscore\n    @property(cc.Node)\n    subScoreNode: cc.Node = null;  //减分背景节点 deductscore\n\n    // 以下变量已不再使用，因为触摸事件已移至ChessBoardController\n    // private isLongPressing: boolean = false;  //是否正在长按\n    // private longPressTimer: number = 0;  //长按计时器\n    // private readonly LONG_PRESS_TIME: number = 1.0;  //长按时间阈值（1秒）\n\n\n\n    // onLoad () {}\n\n    start () {\n        // 禁用PlayerGameController的触摸事件，只使用ChessBoardController的格子触摸事件\n        // 这样避免在已生成的头像上继续生成新的头像\n\n        // 注释掉原来的触摸事件监听\n        // this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);\n        // this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);\n        // this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);\n\n        // 不要在这里强制隐藏旗子节点，让ChessBoardController来控制\n        // 初始化旗子节点为隐藏状态\n        // if (this.flagNode) {\n        //     this.flagNode.active = false;\n        // }\n    }\n\n    setData(user: RoomUser){\n        this.scheduleOnce(() => {\n            if (user == null) {\n                this.avatar.active = false;\n            } else {\n                Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);//添加头像\n                this.avatar.active = true;\n            }\n        }, 0.1);\n    }\n\n    // ===== 以下触摸事件方法已禁用，改由ChessBoardController统一管理 =====\n\n    // 触摸开始事件 (已禁用)\n    // private onTouchStart(_event: cc.Event.EventTouch) {\n    //     this.isLongPressing = true;\n    //     this.longPressTimer = 0;\n    //     // 开始长按检测\n    //     this.schedule(this.checkLongPress, 0.1);\n    // }\n\n    // 触摸结束事件 (已禁用)\n    // private onTouchEnd(event: cc.Event.EventTouch) {\n    //     this.isLongPressing = false;\n    //     this.unschedule(this.checkLongPress);\n    //     // 如果不是长按，则执行点击生成头像预制体的逻辑\n    //     if (this.longPressTimer < this.LONG_PRESS_TIME) {\n    //         this.onTap(event);\n    //     }\n    // }\n\n    // 触摸取消事件 (已禁用)\n    // private onTouchCancel(_event: cc.Event.EventTouch) {\n    //     this.isLongPressing = false;\n    //     this.unschedule(this.checkLongPress);\n    // }\n\n    // 长按检测 (已禁用)\n    // private checkLongPress(dt: number) {\n    //     if (this.isLongPressing) {\n    //         this.longPressTimer += dt;\n    //         // 达到长按时间阈值，显示旗子\n    //         if (this.longPressTimer >= this.LONG_PRESS_TIME) {\n    //             this.onLongPress();\n    //             this.isLongPressing = false;\n    //             this.unschedule(this.checkLongPress);\n    //         }\n    //     }\n    // }\n\n    // 点击事件 - 生成头像预制体（不带旗子）(已禁用)\n    // private onTap(event: cc.Event.EventTouch) {\n    //     console.log(\"点击生成头像（不带旗子）\");\n    //     // 获取触摸位置\n    //     let touchPos = event.getLocation();\n    //     let worldPos = this.node.parent.convertToNodeSpaceAR(touchPos);\n    //     // 复制当前节点作为头像预制体\n    //     let avatarInstance = cc.instantiate(this.node);\n    //     avatarInstance.setPosition(worldPos);\n    //     // 确保生成的头像不带旗子\n    //     let avatarController = avatarInstance.getComponent(PlayerGameController);\n    //     if (avatarController && avatarController.flagNode) {\n    //         avatarController.flagNode.active = false;\n    //     }\n    //     // 添加到父节点\n    //     this.node.parent.addChild(avatarInstance);\n    //     console.log(\"生成了不带旗子的头像预制体\");\n    // }\n\n    // 长按事件 - 生成带旗子的头像预制体 (已禁用)\n    // private onLongPress() {\n    //     console.log(\"长按生成头像（带旗子）\");\n    //     // 获取当前节点的位置作为生成位置\n    //     let currentPos = this.node.getPosition();\n    //     // 复制当前节点作为头像预制体\n    //     let avatarInstance = cc.instantiate(this.node);\n    //     // 设置位置（可以稍微偏移一点避免重叠）\n    //     let offsetPos = cc.v2(currentPos.x + 20, currentPos.y + 20);\n    //     avatarInstance.setPosition(offsetPos);\n    //     // 确保生成的头像带旗子\n    //     let avatarController = avatarInstance.getComponent(PlayerGameController);\n    //     if (avatarController && avatarController.flagNode) {\n    //         avatarController.flagNode.active = true;\n    //         console.log(\"设置旗子节点为可见\");\n    //     } else {\n    //         console.warn(\"生成的头像预制体中找不到旗子节点\");\n    //     }\n    //     // 添加到父节点\n    //     this.node.parent.addChild(avatarInstance);\n    //     console.log(\"生成了带旗子的头像预制体\");\n    // }\n\n\n\n    /**\n     * 显示加分效果，带动画\n     * @param addValue 加分数值\n     */\n    showAddScore(addValue: number) {\n        console.log(`PlayerGameController.showAddScore: +${addValue}`);\n\n        if (this.addScoreNode) {\n            // 获取change_score文本节点并设置加分文本\n            const changeScoreLabel = this.addScoreNode.getChildByName(\"change_score\");\n            if (changeScoreLabel) {\n                const labelComponent = changeScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = \"+\" + addValue.toString();\n                    console.log(`设置PlayerGame change_score文本: ${labelComponent.string}`);\n                }\n            } else {\n                console.warn(\"PlayerGame addScoreNode中找不到change_score子节点\");\n            }\n\n            console.log(\"开始显示PlayerGame addScoreNode动画\");\n\n            // 停止之前的动画\n            cc.Tween.stopAllByTarget(this.addScoreNode);\n\n            // 重置节点状态\n            this.addScoreNode.active = true;\n            this.addScoreNode.opacity = 0;\n            this.addScoreNode.scale = 0.8;\n\n            // 保存原始位置\n            let originalY = this.addScoreNode.y;\n\n            // 使用新的Tween API\n            cc.tween(this.addScoreNode)\n                .parallel(\n                    cc.tween().to(0.15, { opacity: 255 }),\n                    cc.tween().to(0.15, { scale: 1.1 }),\n                    cc.tween().by(0.15, { y: 15 })\n                )\n                .to(0.1, { scale: 1.0 })\n                .delay(0.8)\n                .parallel(\n                    cc.tween().to(0.25, { opacity: 0 }),\n                    cc.tween().to(0.25, { scale: 0.9 }),\n                    cc.tween().by(0.25, { y: 8 })\n                )\n                .call(() => {\n                    this.addScoreNode.active = false;\n                    this.addScoreNode.opacity = 255;\n                    this.addScoreNode.scale = 1.0;\n                    this.addScoreNode.y = originalY;\n                    console.log(\"PlayerGame addScoreNode动画完成\");\n                })\n                .start();\n        } else {\n            console.warn(\"PlayerGame addScoreNode未设置\");\n        }\n    }\n\n    /**\n     * 显示减分效果\n     * @param subValue 减分数值\n     */\n    showSubScore(subValue: number) {\n        console.log(`PlayerGameController.showSubScore: -${subValue}`);\n\n        if (this.subScoreNode) {\n            // 获取change_score文本节点并设置减分文本\n            const changeScoreLabel = this.subScoreNode.getChildByName(\"change_score\");\n            if (changeScoreLabel) {\n                const labelComponent = changeScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = \"-\" + subValue.toString();\n                    console.log(`设置PlayerGame change_score文本: ${labelComponent.string}`);\n                }\n            } else {\n                console.warn(\"PlayerGame subScoreNode中找不到change_score子节点\");\n            }\n\n            this.subScoreNode.active = true;\n\n            // 1秒后隐藏\n            this.scheduleOnce(() => {\n                if (this.subScoreNode) {\n                    this.subScoreNode.active = false;\n                    console.log(\"1秒后隐藏PlayerGame subScoreNode\");\n                }\n            }, 1.0);\n        } else {\n            console.warn(\"PlayerGame subScoreNode未设置\");\n        }\n    }\n\n    // 隐藏加减分节点\n    hideScoreEffects() {\n        if (this.addScoreNode) {\n            this.addScoreNode.active = false;\n        }\n        if (this.subScoreNode) {\n            this.subScoreNode.active = false;\n        }\n    }\n\n    // update (dt) {}\n}\n"]}