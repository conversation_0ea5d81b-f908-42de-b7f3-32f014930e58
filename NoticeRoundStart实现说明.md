# NoticeRoundStart 扫雷回合开始通知实现说明

## 概述

根据API.md文档，实现了接收`NoticeRoundStart`消息并同步更新前端页面计时的功能。

## 实现内容

### 1. 消息ID定义 (MessageId.ts)

在`assets/scripts/net/MessageId.ts`中添加了扫雷游戏相关的消息ID：

```typescript
// 扫雷游戏相关消息
MsgTypeNoticeRoundStart = 'NoticeRoundStart',     // 扫雷回合开始通知
MsgTypeNoticeActionDisplay = 'NoticeActionDisplay', // 扫雷操作展示通知
MsgTypeNoticeRoundEnd = 'NoticeRoundEnd',         // 扫雷回合结束通知
MsgTypeNoticeFirstChoiceBonus = 'NoticeFirstChoiceBonus', // 扫雷首选玩家奖励通知
MsgTypeNoticeGameEnd = 'NoticeGameEnd',           // 扫雷游戏结束通知
MsgTypeClickBlock = 'ClickBlock',                 // 点击方块
```

### 2. 数据结构定义 (GameBean.ts)

在`assets/scripts/bean/GameBean.ts`中添加了扫雷游戏相关的接口定义：

- `NoticeRoundStart`: 扫雷回合开始通知
- `NoticeActionDisplay`: 扫雷操作展示通知
- `NoticeRoundEnd`: 扫雷回合结束通知
- `NoticeFirstChoiceBonus`: 扫雷首选玩家奖励通知
- `NoticeGameEnd`: 扫雷游戏结束通知
- `MineBlock`: 扫雷方块结构
- `ClickBlockRequest`: 点击方块请求

### 3. 游戏页面控制器 (GamePageController.ts)

在`assets/scripts/game/GamePageController.ts`中添加了：

#### 属性
- `timeLabel: cc.Label`: 计时器显示标签
- `countdownInterval: number`: 倒计时定时器ID
- `currentCountdown: number`: 当前倒计时秒数
- `currentRoundNumber: number`: 当前回合编号

#### 方法
- `onNoticeRoundStart(data: NoticeRoundStart)`: 处理扫雷回合开始通知
- `startCountdown(seconds: number)`: 开始倒计时
- `updateCountdownDisplay(seconds: number)`: 更新倒计时显示
- `clearCountdownTimer()`: 清除倒计时定时器

#### 自动查找UI组件
在`onLoad()`方法中添加了自动查找`time_label`组件的逻辑，如果在编辑器中没有设置`timeLabel`属性，会自动通过路径查找。

### 4. 全局消息处理 (GlobalManagerController.ts)

在`assets/scripts/GlobalManagerController.ts`的消息处理switch语句中添加了：

```typescript
case MessageId.MsgTypeNoticeRoundStart: //扫雷回合开始通知
    console.log('收到扫雷回合开始通知:', messageBean.data);
    // 只有在游戏页面时才处理此消息
    if (this.currentPage === PageType.GAME_PAGE) {
        this.gamePageController.onNoticeRoundStart(messageBean.data);
    }
    break;
```

### 5. 测试脚本 (NoticeRoundStartTest.ts)

创建了测试脚本`assets/scripts/test/NoticeRoundStartTest.ts`，可以用来：
- 模拟发送NoticeRoundStart消息
- 测试不同的倒计时值
- 验证前端计时器更新功能

## 功能特点

### 1. 消息处理流程
1. WebSocket接收到`NoticeRoundStart`消息
2. `WebSocketManager`解析消息并发送事件
3. `GlobalManagerController`接收事件并路由到游戏页面
4. `GamePageController`处理消息并更新计时器

### 2. 计时器功能
- 自动开始25秒倒计时
- 每秒更新显示
- 倒计时结束自动清理
- 支持中途更新倒计时值

### 3. UI组件查找
- 支持编辑器拖拽设置
- 支持自动路径查找
- 兼容现有场景结构

### 4. 错误处理
- 空值检查
- 定时器清理
- 页面状态检查

## 使用方法

### 1. 编辑器设置
在游戏场景中，将`GamePageController`组件的`timeLabel`属性拖拽设置为`time_bg/time_label`节点。

### 2. 代码调用
当收到服务器的`NoticeRoundStart`消息时，系统会自动处理并更新计时器显示。

### 3. 测试
可以使用`NoticeRoundStartTest`脚本进行功能测试。

## API消息格式

根据API.md，`NoticeRoundStart`消息格式为：

```json
{
  "msgId": "NoticeRoundStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "countDown": 25,
    "gameStatus": 0
  }
}
```

## 注意事项

1. 确保游戏场景中存在`time_bg/time_label`节点
2. 计时器会在页面切换时自动清理
3. 只有在游戏页面时才会处理NoticeRoundStart消息
4. 倒计时显示格式为"XXs"（如"25s"）

## 扩展功能

后续可以基于此实现添加：
- 其他扫雷游戏消息的处理
- 更复杂的UI动画效果
- 音效提示
- 倒计时警告效果
